import { OverrideByFactoryOptions, Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { CoreService } from './core.service';
import { RedisModule } from '@nestjs-modules/ioredis';
import { RedisService } from './redis.service';
import { of } from 'rxjs';
import { DomainEventsService } from './domain-events.service';

describe('CoreService', () => {
  let service: CoreService;
  let httpService: HttpService;
  let configService: ConfigService;
  let domainService: DomainEventsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        RedisModule
      ],
      providers: [
        RedisService,
        CoreService,
        HttpService,
        ConfigService,
        DomainEventsService
      ],
    })
    .overrideProvider(RedisService)
    .useFactory({
      factory: () => {
        const mockService = {
          getClient: jest.fn(),
        };
        const mockRedis = {
          get: jest.fn(),
          set: jest.fn(),
          del: jest.fn(),
        };
        mockService.getClient.mockReturnValue(mockRedis);
        return mockService;
      },
    } as OverrideByFactoryOptions)
    .overrideProvider(HttpService)
    .useValue({
      get: jest.fn(),
      post: jest.fn(),
    })
    .overrideProvider(ConfigService)
    .useValue({
      get: jest.fn((key: string) => {
        if (key === 'db.serviceTokens.readWrite') return 'dummy_auth_token';
        if (key === 'API_CORE_URL') return 'http://api.example.com';
        return '';
      }),
    })
    .overrideProvider(DomainEventsService)
    .useValue({
      publish: jest.fn(),
    })
    .compile();

    service = module.get<CoreService>(CoreService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
    domainService = module.get<DomainEventsService>(DomainEventsService);
  });

  describe('isFeatureEnabled', () => {
    it('should check feature status with correct parameters', async () => {
      const mockResponse = true;

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));
      await service.isFeatureEnabled('feature1', 1);
      expect(httpService.get).toHaveBeenCalledWith(
        'http://api.example.com/featuretoggle?filterAccount_id=1&filterFeature_name=feature1',
        { headers: { Authorization: 'dummy_auth_token' } }
      );
    });
  });

  describe('Payment Token Operations', () => {
    it('should call encode with correct parameters', async () => {
      const paymentToken = {
        cardNumber: '****************',
        securityCode: '123',
        expiration: '12/21',
        postalCode: '12345'
      };

      (httpService.post as jest.Mock).mockReturnValue(of({
        type: 'encode',
        terms_list: [
          paymentToken.cardNumber,
          paymentToken.securityCode,
          paymentToken.expiration,
          paymentToken.postalCode
        ]
      }));

      await service.encodePaymentToken(paymentToken);

      expect(httpService.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          type: 'encode',
          terms_list: expect.arrayContaining([
            paymentToken.cardNumber,
            paymentToken.securityCode,
            paymentToken.expiration,
            paymentToken.postalCode
          ])
        }),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token'
          })
        })
      );
    });

    it('should call decode with correct parameters', async () => {
      const paymentToken = {
        cardNumber: '****************',
        securityCode: '123',
        expiration: '12/21',
        postalCode: '12345'
      };

      (httpService.post as jest.Mock).mockReturnValue(of({
        type: 'decode',
        terms_list: [
          paymentToken.cardNumber,
          paymentToken.securityCode,
          paymentToken.expiration,
          paymentToken.postalCode
        ]
      }));


      await service.decodePaymentToken(paymentToken);
      expect(httpService.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          type: 'decode',
          terms_list: expect.arrayContaining([
            paymentToken.cardNumber,
            paymentToken.securityCode,
            paymentToken.expiration,
            paymentToken.postalCode
          ])
        }),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token'
          })
        })
      );
    });
  });

  describe('CoreService', () => {
    describe('createSession', () => {
      it('should create a session with correct parameters', async () => {
        const sessionRequest = { user_id: 1 };
        (httpService.post as jest.Mock).mockReturnValue(of(sessionRequest));
        await service.createSession(sessionRequest);
        expect(httpService.post).toHaveBeenCalledWith(
          'http://api.example.com/session',
          sessionRequest,
          { headers: { Authorization: 'dummy_auth_token' } }
        );
      });
    });
  });
});
