import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { TenantDataRequest } from '../models/tenant.model';
import { of, throwError } from 'rxjs';
import { IntegrationService } from './integration.service';
import { AmountDueRequest } from '../models/payment.model';
import { DomainEventsService } from './domain-events.service';
import { AxiosError } from 'axios';
import { Ledger } from '../models/ledger.model';
import { BugsnagService } from './bugsnag.service';
import Bugsnag from '@bugsnag/js';

Bugsnag.start({
  apiKey: '616b106fc4dba0412968d8c0e91995bey',
  releaseStage: 'test',
  appVersion: '1.0.0',
});

jest.spyOn(Bugsnag, 'start').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'leaveBreadcrumb').mockImplementation(jest.fn());
jest.spyOn(Bugsnag, 'notify').mockImplementation(jest.fn());

describe('IntegrationService', () => {
  let service: IntegrationService;
  let httpService: HttpService;
  let configService: ConfigService;
  let domainService: DomainEventsService;
  let bugsnagService: BugsnagService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IntegrationService,
        HttpService,
        ConfigService,
        DomainEventsService,
        {
          provide: BugsnagService,
          useValue: {
            leaveBreadcrumb: jest.fn(),
            notify: jest.fn(),
            start: jest.fn(),
          },
        },
      ],
    })
      .overrideProvider(HttpService)
      .useValue({
        get: jest.fn(),
        post: jest.fn(),
      })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key: string) => {
          if (key === 'db.serviceTokens.readWrite') return 'dummy_auth_token';
          if (key === 'API_INT_URL') return 'http://api.example.com';
          return '';
        }),
      })
      .overrideProvider(DomainEventsService)
      .useValue({
        publish: jest.fn(),
      })
      .compile();

    service = module.get<IntegrationService>(IntegrationService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
    domainService = module.get<DomainEventsService>(DomainEventsService);
    bugsnagService = module.get<BugsnagService>(BugsnagService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getTenantData', () => {
    it('should call HttpService.get with correct parameters', async () => {
      const request = {
        locationId: 1,
        filterPhone: '**********',
      } as TenantDataRequest;
      const mockResponse = {
        data: {
          status: 'OK',
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));

      await service.getTenantData(request);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api.example.com/tenant?locationId=1&filterPhone=**********&filterActive=true&page=1`,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
    });

    it('should handle errors in getTenantData', async () => {
      const request = {
        locationId: 1,
        filterPhone: '**********',
      } as TenantDataRequest;

      (httpService.get as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      const result = await service.getTenantData(request);
      expect(result).toEqual({});
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });

  describe('getTenantById', () => {
    it('should fetch tenant data by ID', async () => {
      const tenantId = 'tenant123';
      const mockResponse = {
        data: {
          id: tenantId,
          name: 'Test Tenant',
        },
      };

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));
      const result = await service.getTenantById(tenantId);
      expect(result).toEqual(mockResponse.data);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api.example.com/tenant/${tenantId}`,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
    });

    it('should handle errors in getTenantById', async () => {
      const tenantId = 'tenant123';
      (httpService.get as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      const result = await service.getTenantById(tenantId);
      expect(result).toBeNull();
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });

  describe('getLedgerData', () => {
    it('should fetch ledger data for a tenant', async () => {
      const tenantId = 'tenant123';
      const mockResponse = {
        data: {
          status: 'OK',
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));
      await service.getLedgerData(tenantId);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api.example.com/tenant/${tenantId}/ledger`,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
    });

    it('should handle errors in getLedgerData', async () => {
      const tenantId = 'tenant123';
      (httpService.get as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      const result = await service.getLedgerData(tenantId);
      expect(result).toEqual({});
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });

  describe('getSavedCards', () => {
    it('should fetch saved cards for a tenant', async () => {
      const tenantId = 'tenant123';
      const mockResponse = {
        data: {
          status: 'OK',
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));
      await service.getSavedCards(tenantId);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api.example.com/tenant/${tenantId}/saved_cards`,
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
    });

    it('should handle errors in getSavedCards', async () => {
      const tenantId = 'tenant123';
      (httpService.get as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      await expect(service.getSavedCards(tenantId)).rejects.toThrow('API Error');
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });

  describe('getAmountDue', () => {
    it('should call HttpService.post with correct parameters', async () => {
      const tenantId = 'tenant123';
      const mockResponse = {
        data: {
          status: 'OK',
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      const request = [
        { ledger_id: 1, unit_id: 'unit123', prepay_month: 1 },
      ] as AmountDueRequest[];
      await service.getAmountDue(tenantId, request);
      expect(httpService.post).toHaveBeenCalledWith(
        `http://api.example.com/tenant/${tenantId}/amount_due`,
        JSON.stringify(request),
        {
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        }
      );
    });

    it('should handle errors in getAmountDue', async () => {
      const tenantId = 'tenant123';
      const request = [{ ledger_id: 1, unit_id: 'unit123', prepay_month: 1 }];
      (httpService.post as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      const result = await service.getAmountDue(tenantId, request);
      expect(result).toEqual({ error: true });
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });

  describe('createPaymentHash', () => {
    it('should create a payment hash', async () => {
      const request = {
        customer_id: 'customer123',
        location_id: 1,
        ledger_id: 1,
      };
      (httpService.post as jest.Mock).mockReturnValue(of(request));
      await service.createPaymentHash(request);
      expect(httpService.post).toHaveBeenCalledWith(
        'http://api.example.com/payment_hash',
        {
          ...request,
          source: 'call',
        },
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
    });

    it('should handle errors in createPaymentHash', async () => {
      const request = {
        customer_id: 'customer123',
        location_id: 1,
        ledger_id: 1,
      };
      (httpService.post as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      await expect(service.createPaymentHash(request)).rejects.toThrow('API Error');
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });

  describe('getPaymentHashDetails', () => {
    it('should get details of a payment hash', async () => {
      const paymentHash = 'hash123';
      const mockResponse = {
        data: {
          status: 'OK',
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.get as jest.Mock).mockReturnValue(of(mockResponse));
      await service.getPaymentHashDetails(paymentHash);
      expect(httpService.get).toHaveBeenCalledWith(
        `http://api.example.com/payment_hash/${paymentHash}`,
        {
          headers: {
            Authorization: 'dummy_auth_token',
          },
        }
      );
    });

    it('should handle errors in getPaymentHashDetails', async () => {
      const paymentHash = 'hash123';
      (httpService.get as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      await expect(service.getPaymentHashDetails(paymentHash)).rejects.toThrow('API Error');
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });

  describe('makeTenantPayment', () => {
    it('should make a payment for a tenant', async () => {
      const tenantId = 'tenant123';
      const mockResponse = {
        data: {
          status: 'OK',
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      const paymentData = {
        amount: 100,
        convenience_fee: false,
        is_auto_pay: false,
        pay_method: 'credit',
        payment_hash: 'hash123',
        save_cc: false,
        total_amount_due: 100,
        unit_id: 'unit123',
        cvc_number: '123',
        postal_code: '12345',
        payment_id: 'payment123',
      };
      await service.makeTenantPayment(tenantId, paymentData);
      expect(httpService.post).toHaveBeenCalledWith(
        `http://api.example.com/tenant/${tenantId}/payment`,
        JSON.stringify(paymentData),
        {
          headers: {
            Authorization: 'dummy_auth_token',
          },
        }
      );
    });

    it('should handle invalid test card in makeTenantPayment', async () => {
      const tenantId = 'tenant123';
      const paymentData = {
        amount: 100,
        card_number: '3005739193013242',
        convenience_fee: false,
        is_auto_pay: false,
        pay_method: 'credit',
        payment_hash: 'hash123',
        save_cc: false,
        total_amount_due: 100,
        unit_id: 'unit123',
        cvc_number: '123',
        postal_code: '12345',
        payment_id: 'payment123',
      };

      const result = await service.makeTenantPayment(tenantId, paymentData);
      expect(result).toEqual({
        data: {
          success: false,
          error_msg: 'Payment failed due to use of Invalid Test Card (makeTenantPayment)'
        },
        status: 500
      });
    });

    it('should handle AxiosError during payment', async () => {
      const tenantId = 'tenant123';
      const paymentData = {
        amount: 100,
        convenience_fee: false,
        is_auto_pay: false,
        pay_method: 'credit',
        payment_hash: 'hash123',
        save_cc: false,
        total_amount_due: 100,
        unit_id: 'unit123',
        cvc_number: '123',
        postal_code: '12345',
        payment_id: 'payment123',
      };

      const axiosError = new AxiosError();
      axiosError.response = {
        data: { error_msg: 'Payment failed' },
        status: 400,
        statusText: 'Bad Request',
        headers: {},
        config: {} as any
      };

      (httpService.post as jest.Mock).mockReturnValue(throwError(() => axiosError));

      const result = await service.makeTenantPayment(tenantId, paymentData);

      expect(result).toEqual({
        data: {
          success: false,
          error_msg: 'Payment failed'
        },
        status: 400
      });
    });

    it('should handle unexpected errors during payment', async () => {
      const tenantId = 'tenant123';
      const paymentData = {
        amount: 100,
        convenience_fee: false,
        is_auto_pay: false,
        pay_method: 'credit',
        payment_hash: 'hash123',
        save_cc: false,
        total_amount_due: 100,
        unit_id: 'unit123',
        cvc_number: '123',
        postal_code: '12345',
        payment_id: 'payment123',
      };

      (httpService.post as jest.Mock).mockReturnValue(throwError(() => new Error('Unexpected error')));

      const result = await service.makeTenantPayment(tenantId, paymentData);

      expect(result).toEqual({
        data: {
          success: false,
          error_msg: 'An unexpected error occurred'
        },
        status: 500
      });
    });
  });

  describe('makeTenantBulkPayment', () => {
    it('should make bulk payments for a tenant', async () => {
      const tenantId = 'tenant123';
      const mockResponse = {
        data: {
          status: 'OK',
          items: [] as any,
          paging: {
            current_page: 0,
          },
        },
      };

      (httpService.post as jest.Mock).mockReturnValue(of(mockResponse));
      const paymentDataArray = [
        {
          amount: 100,
          convenience_fee: false,
          is_auto_pay: false,
          pay_method: 'credit',
          payment_hash: 'hash123',
          save_cc: false,
          total_amount_due: 100,
          unit_id: 'unit123',
          cvc_number: '123',
          postal_code: '12345',
          payment_id: 'payment123',
        },
        {
          amount: 100,
          convenience_fee: false,
          is_auto_pay: false,
          pay_method: 'credit',
          payment_hash: 'hash321',
          save_cc: false,
          total_amount_due: 100,
          unit_id: 'unit321',
          cvc_number: '321',
          postal_code: '54321',
          payment_id: 'payment321',
        },
      ];
      await service.makeTenantBulkPayment(tenantId, paymentDataArray);
      expect(httpService.post).toHaveBeenCalledWith(
        `http://api.example.com/tenant/${tenantId}/payment_bulk`,
        JSON.stringify(paymentDataArray),
        {
          headers: {
            Authorization: 'dummy_auth_token',
          },
        }
      );
    });

    it('should handle invalid test card in makeTenantBulkPayment', async () => {
      const tenantId = 'tenant123';
      const paymentDataArray = [
        {
          amount: 100,
          card_number: '3005739193013242',
          convenience_fee: false,
          is_auto_pay: false,
          pay_method: 'credit',
          payment_hash: 'hash123',
          save_cc: false,
          total_amount_due: 100,
          unit_id: 'unit123',
          cvc_number: '123',
          postal_code: '12345',
          payment_id: 'payment123',
        }
      ];

      const result = await service.makeTenantBulkPayment(tenantId, paymentDataArray);
      expect(result).toEqual({
        data: {
          success: false,
          error_msg: 'Payment failed due to use of Invalid Test Card (makeTenantBulkPayment)'
        },
        status: 500
      });
    });

    it('should handle AxiosError during bulk payment', async () => {
      const tenantId = 'tenant123';
      const paymentDataArray = [
        {
          amount: 100,
          convenience_fee: false,
          is_auto_pay: false,
          pay_method: 'credit',
          payment_hash: 'hash123',
          save_cc: false,
          total_amount_due: 100,
          unit_id: 'unit123',
          cvc_number: '123',
          postal_code: '12345',
          payment_id: 'payment123',
        },
      ];

      const axiosError = new AxiosError();
      axiosError.response = {
        data: { error_msg: 'Bulk payment failed' },
        status: 400,
        statusText: 'Bad Request',
        headers: {},
        config: {} as any
      };

      (httpService.post as jest.Mock).mockReturnValue(throwError(() => axiosError));

      const result = await service.makeTenantBulkPayment(tenantId, paymentDataArray);

      expect(result).toEqual({
        data: {
          success: false,
          error_msg: 'Bulk payment failed'
        },
        status: 400
      });
    });

    it('should handle unexpected errors during bulk payment', async () => {
      const tenantId = 'tenant123';
      const paymentDataArray = [
        {
          amount: 100,
          convenience_fee: false,
          is_auto_pay: false,
          pay_method: 'credit',
          payment_hash: 'hash123',
          save_cc: false,
          total_amount_due: 100,
          unit_id: 'unit123',
          cvc_number: '123',
          postal_code: '12345',
          payment_id: 'payment123',
        },
      ];

      (httpService.post as jest.Mock).mockReturnValue(throwError(() => new Error('Unexpected error')));

      const result = await service.makeTenantBulkPayment(tenantId, paymentDataArray);

      expect(result).toEqual({
        data: {
          success: false,
          error_msg: 'An unexpected error occurred'
        },
        status: 500
      });
    });
  });

  describe('calculateAmountDue', () => {
    it('should calculate total amount due for multiple units', async () => {
      const units = [
        { tenant_id_es: 'tenant1', es_unit_id: 'unit1', ledger_id: 1 },
        { tenant_id_es: 'tenant2', es_unit_id: 'unit2', ledger_id: 2 },
      ] as Ledger[];

      const mockResponse1 = {
        data: [{
          ledger_id: '1',
          unit_id: 'unit1',
          prepay_month: 0,
          data: [],
          meta: { total: '100.00', breakup_match: true },
        }],
      };
      const mockResponse2 = {
        data: [{
          ledger_id: '2',
          unit_id: 'unit2',
          prepay_month: 0,
          data: [],
          meta: { total: '200.00', breakup_match: true },
        }],
      };

      (httpService.post as jest.Mock)
        .mockReturnValueOnce(of(mockResponse1))
        .mockReturnValueOnce(of(mockResponse2));

      const result = await service.calculateAmountDue(units);

      expect(httpService.post).toHaveBeenCalledTimes(2);
      expect(httpService.post).toHaveBeenCalledWith(
        'http://api.example.com/tenant/tenant1/amount_due',
        JSON.stringify([{ ledger_id: 1, unit_id: 'unit1', prepay_month: 0 }]),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
      expect(httpService.post).toHaveBeenCalledWith(
        'http://api.example.com/tenant/tenant2/amount_due',
        JSON.stringify([{ ledger_id: 2, unit_id: 'unit2', prepay_month: 0 }]),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
      expect(units[0].amount_owed).toBe('100.00');
      expect(units[1].amount_owed).toBe('200.00');
      expect(result).toBe(300);
    });

    it('should throw an error when getAmountDue returns an error', async () => {
      const units = [
        { tenant_id_es: 'tenant1', es_unit_id: 'unit1', ledger_id: 1 },
        { tenant_id_es: 'tenant2', es_unit_id: 'unit2', ledger_id: 2 },
      ] as Ledger[];

      const mockErrorResponse = {
        data: { error: true },
      };

      (httpService.post as jest.Mock).mockReturnValueOnce(of(mockErrorResponse));

      await expect(service.calculateAmountDue(units)).rejects.toThrow(
        'Error retrieving the amount due for tenant tenant1 {"ledger_id":1,"unit_id":"unit1","prepay_month":0}'
      );

      expect(httpService.post).toHaveBeenCalledTimes(1);
      expect(httpService.post).toHaveBeenCalledWith(
        'http://api.example.com/tenant/tenant1/amount_due',
        JSON.stringify([{ ledger_id: 1, unit_id: 'unit1', prepay_month: 0 }]),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'dummy_auth_token',
          }),
        })
      );
      expect(units[0].amount_owed).toBeUndefined();
      expect(units[1].amount_owed).toBeUndefined();
    });

    it('should handle API errors in calculateAmountDue', async () => {
      const units = [
        { tenant_id_es: 'tenant1', es_unit_id: 'unit1', ledger_id: 1 },
      ] as Ledger[];

      (httpService.post as jest.Mock).mockReturnValue(throwError(() => new Error('API Error')));

      await expect(service.calculateAmountDue(units)).rejects.toThrow(
        'Error retrieving the amount due for tenant tenant1 {"ledger_id":1,"unit_id":"unit1","prepay_month":0}'
      );
      expect(bugsnagService.notify).toHaveBeenCalled();
    });
  });
});