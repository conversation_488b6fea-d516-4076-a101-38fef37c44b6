import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse, PayByPhoneStateBase } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";

@Injectable()
export class FinalPayAmountPrompt extends PayByPhoneStateBase {
  override async handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponse> {
    const { storage, twilioResponse } = context;

    const gatherOptions = {
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    };

    twilioResponse.gatherWithLocaleSay(gatherOptions, [
      {
        messageId: 'pay-by-phone.amount-due',
        locale: storage.locale,
        i18nOptions: { args: [{ paymentAmount: (storage.totalAmountDue + storage.convenienceFee).toFixed(2) }] }
      },
      {
        messageId: 'pay-by-phone.confirm-payment',
        locale: storage.locale
      }
    ]);

    return { nextState: PayByPhoneState.FinalPayAmountConfirm };
  }
}
