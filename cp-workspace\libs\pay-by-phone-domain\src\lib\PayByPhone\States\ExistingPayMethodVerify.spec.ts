import { ExistingPayMethodVerify } from './ExistingPayMethodVerify';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('ExistingPayMethodVerify', () => {
  let service: ExistingPayMethodVerify;

  beforeEach(() => {
    service = new ExistingPayMethodVerify();
  });

  it('should transition to PayMethodSecurityCodePrompt if a matching card is found', async () => {
    const context = {
      storage: {
        lastFourDigits: '1234',
        savedCards: [{ card_number: '1234' }],
        totalBalance: 0,
        totalAmountDue: 0
      },
      twilioResponse: {
        sayInLocale: jest.fn(),
      },
    } as any;

    const result = await service.handler(context);

    expect(result).toEqual({
      nextState: PayByPhoneState.PayMethodSecurityCodePrompt,
    });
  });

  it('should say we were unable to find a matching card and transition to PayMethodCreditCardPrompt if no matching card is found', async () => {
    const context = {
      storage: {
        lastFourDigits: '1234',
        savedCards: [{ card_number: '1111111111111111' }],
        locale: 'en',
        totalBalance: 0,
        totalAmountDue: 0
      },
      twilioResponse: {
        sayInLocale: jest.fn(),
      },
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({messageId: "pay-by-phone.could-not-find-card", locale: context.storage.locale});
    expect(result).toEqual({
      nextState: PayByPhoneState.PayMethodCreditCardPrompt,
    });
  });

  it('should say we were unable to find a matching card and transition to PayMethodCreditCardPrompt if no saved cards', async () => {
    const context = {
      storage: {
        lastFourDigits: '1234',
        locale: 'en',
        totalBalance: 0,
        totalAmountDue: 0
      },
      twilioResponse: {
        sayInLocale: jest.fn(),
      },
    } as any;

    const result = await service.handler(context);

    expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({messageId: "pay-by-phone.could-not-find-card", locale: context.storage.locale});
    expect(result).toEqual({
      nextState: PayByPhoneState.PayMethodCreditCardPrompt,
    });
  });
});
