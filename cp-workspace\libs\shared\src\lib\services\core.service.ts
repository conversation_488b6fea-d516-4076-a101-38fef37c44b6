import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { CallPotentialHttpApiService } from './callpotential-http-api.service';
import { RedisService } from './redis.service';
import { Cacheable } from '../decorators/cacheable.decorator';
import { PaymentToken } from '../models/payment.model';
import { Session, CreateSessionRequest } from '../models/session.model';
import { AccountId } from '../models/location-configuration.model';
import { DomainEventsService } from './domain-events.service';

@Injectable()
export class CoreService extends CallPotentialHttpApiService {
    constructor(
        protected override httpService: HttpService,
        protected override configService: ConfigService,
        protected override domainEventService: DomainEventsService,
        public redisService: RedisService,
    ) {
        super(httpService, configService, domainEventService, 'API_CORE_URL');
    }

    @Cacheable(5 * 60)
    public async isFeatureEnabled(featureName: string, accountId: AccountId): Promise<boolean> {
        const endpoint = '/featuretoggle';
        const params = {
            filterAccount_id: accountId,
            filterFeature_name: featureName,
        };
        const featureToggle = await this.get(endpoint, params);
        return featureToggle?.items?.[0]?.feature_value === 1;
    }

    private async processPaymentToken(paymentToken: PaymentToken, operationType: 'encode' | 'decode'): Promise<PaymentToken> {
        const termsList: string[] = [];
        const definedFields: (keyof PaymentToken)[] = [];
        for (const key of ['cardNumber', 'securityCode', 'expiration', 'postalCode'] as const) {
            if (paymentToken[key] !== undefined) {
                termsList.push(paymentToken[key]!);
                definedFields.push(key);
            }
        }
        if (termsList.length === 0) {
            return {};
        }

        const endpoint = '/encodetoken';
        const body = {
            type: operationType,
            terms_list: termsList,
        };
        const response = (await this.post(endpoint, body)).data;
        const processedToken: PaymentToken = {};
        if(!response) return processedToken;
        
        response.forEach((value: string, index: number) => {
            const field = definedFields[index];
            processedToken[field] = value;
        });
        return processedToken;
    } 

    public async encodePaymentToken(paymentToken: PaymentToken): Promise<PaymentToken> {
        return this.processPaymentToken(paymentToken, 'encode');
    }
    
    public async decodePaymentToken(paymentToken: PaymentToken): Promise<PaymentToken> {
        return this.processPaymentToken(paymentToken, 'decode');
    }
    
    public async createSession(request: CreateSessionRequest): Promise<Session> {
        const endpoint = '/session';
        return (await this.post(endpoint, request)).data as Session;
    }
}
