const config = require('../config/config');
const DynamoHelper = require('./shared/dynamo-helper'); // NOTE: WIP -- need to rename this file
const CallDetail = require('../libraries/calldetail')
const bugsnagService = require('../libraries/bugsnag-service');

// ASH This sets the config for the chat workspace handler
const chatTwilioHelpers = require('@callpotential/CP-Text-Messaging-Services/src/integration/feature-config');
chatTwilioHelpers.setConfig(config);

const AWS = require('aws-sdk');
const express = require('express');
const Twilio = require('twilio');
const TwUtils = require('./utils/twillio');
const console_c = require('../config/logger').console;
const moment = require('moment-timezone');

AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });
const router = express.Router();

const chatWorkspaceEventHandler = require('@callpotential/CP-Text-Messaging-Services/src/integration/workspace-event-handler')
const ccWorkspaceEventHandler = require('../libraries/workspace-event-handler');

const dataDogService = require('../libraries/shared/datadog-service');


Array.prototype.forEachAsync = async function (fn) {
  for (let t of this) { await fn(t) }
}

router.route('/workspace_event_callback/:cc_type?')
  .post(async function (req, res) {

    if (req.body.WorkflowName === 'Chat' || req.body.TaskChannelUniqueName === 'chat'){
      if (req.body.EventType !== 'worker.capacity.update'){
        return await chatWorkspaceEventHandler(req, res);
      }
    }

    return await ccWorkspaceEventHandler(req, res);
  })

router.route('/inbound')
  .get(function (req, res) {
    res.set('Content-Type', 'text/xml');
    res.send("<Response><Say>Request with invalid parameters.</Say></Response>");
  })
  .post(async function (req, res) {
    var data = req.body;
    let callRouteId = req.header('SipHeader_X-cproute');

    if (callRouteId) {
      data.callRouteId = callRouteId;
    }

    console_c.log("# inbound data #", JSON.stringify(data));

    res.set('Content-Type', 'text/xml');
    if (!data.To) {
      var xml_res = "<Response><Say>Request with invalid parameters.</Say></Response>"
      res.send(xml_res);
      return;
    }
    try{
      let result = await TwUtils.process_inbound_call(data);
      res.send(result);
    }
    catch (e)
    {
      console.error(e, new Error().stack);
      // Add Bugsnag error tracking
      bugsnagService.notify(e, {
        metadata: {
          call: {
            callSid: data.CallSid,
            error: 'Inbound call processing failed',
            stack: e.stack
          }
        }
      });
      var twimlResponse = "<Response><Say>Something wrong with processing call.</Say></Response>";
      res.send(twimlResponse);
      return;
    }
  })

router.route('/process_next_step/:log_id/')
  .post(function (req, res) {
    const startTime = Date.now();
    const CallRoute = require('../libraries/call-route').CallRoute;
    const CallLogModel = require('../models/call-log');
    const req_q_data = req.query;

    console_c.log('QUERY', req.query);
    console_c.log('PARAMS', req.params);
    console_c.log('BODY', req.body);

    const req_p_data = {};
    if (req.body) {
      Object.assign(req_p_data, req.body);
    }

    req_p_data.req_url = req.url;

    console_c.log("# process next step req_q_data #", JSON.stringify(req_q_data));

    const callRoute = new CallRoute({ log_id: req.params.log_id }, req_q_data, req_p_data);

    new CallLogModel().list_dynamodb(req.params.log_id)
      .then((log_info) => {
        if (callRoute.exit === true) {
          console_c.log("# exit call route #", log_info);
          callRoute.chk_or_exit_update_call_log(req_q_data, req.params.log_id, log_info, req_p_data).then(function () {
            res.set('Content-Type', 'text/xml');
            return res.send('<Response></Response>');
          });
        } else {
          // Get call log info for log_id
          // console_c.log("# No exit ############ #", log_info);
          console_c.log("# set_call_info 1# ", log_info);
          callRoute.set_call_info(req.params.log_id, log_info)
            .then(() => {
              callRoute.current_step = callRoute.call_info.current_route_step ? JSON.parse(callRoute.call_info.current_route_step) : '';
              callRoute.is_route_complete = callRoute.call_info.is_route_complete;
              callRoute.location_id = callRoute.call_info.location_id;

              if (req_p_data.DialCallStatus
                // Request for matching pressed digit against list of pre-specified values
                ||
                req_p_data.Digits
                // Request for handling recorded voicemail message
                ||
                ('handle_message' in req_q_data)
                // Request for call to leave queue or while waiting in queue
                ||
                req_q_data.leave_queue || req_q_data.wait_in_queue
                // Request to simply process next step
                ||
                ('next_step' in req_q_data)
                // Request for ad whisper
                ||
                req_q_data.whisper_ad
                // Request for payment_process
                ||
                req_q_data.payment_process) {
                // Also to be handled -> connect with available agents call request
                // console_c.log("######## get_next_step ########", JSON.stringify(req_q_data));
                callRoute.get_next_step(req_q_data, req_p_data).then(function (xml_res) {
                  const isOldPayByPhone = req_q_data.payment_process === '1';
                  if(isOldPayByPhone)
                  {
                    const duration = Date.now() - startTime;
                    dataDogService.recordDistribution('old_pay_by_phone.request.duration', duration);
                    dataDogService.incrementCounter('old_pay_by_phone.request.total');
                  }

                  res.set('Content-Type', 'text/xml');
                  return res.send(xml_res);
                });
              } else {
                res.set('Content-Type', 'text/xml');
                return res.send('<Response><Say>Request with invalid parameters.</Say></Response>');
              }
            });
        }
      });
  })

router.route('/twilio_status_callback/')
  .get(function (req, res) {
    var req_q_data = req.query;

    console_c.log('TWILIO STATUS CB GET', req_q_data);

    // Add Bugsnag tracking for GET requests
    bugsnagService.leaveBreadcrumb('twilio_status_callback GET request', {
      queryParams: req_q_data,
      hasLogId: !!(req_q_data?.log_id)
    });

    TwUtils.twilio_status_callback(req_q_data).then(function (twiml_res) {
      var xml_res = twiml_res.toString();
      res.set('Content-Type', 'text/xml');
      res.send(xml_res);
    });
  })
  .post(function (req, res) {
    var req_q_data = req.query;
    var p_data = req.body

    console_c.log('TWILIO STATUS CB POST', req_q_data, p_data);

    // Add Bugsnag tracking for POST requests
    bugsnagService.leaveBreadcrumb('twilio_status_callback POST request', {
      queryParams: req_q_data,
      bodyKeys: p_data ? Object.keys(p_data) : [],
      callSid: p_data?.CallSid,
      callStatus: p_data?.CallStatus,
      recordingStatus: p_data?.RecordingStatus,
      hasRecordingUrl: !!(p_data?.RecordingUrl),
      hasLogId: !!(req_q_data?.log_id)
    });

    TwUtils.twilio_status_callback(req_q_data, p_data).then(function (twiml_res) {
      var xml_res = twiml_res.toString();
      res.set('Content-Type', 'text/xml');
      res.send(xml_res);
    });
  })

/**
 * Echoes xml response for call which has left
 * queue as a result of post update from controllers/miscellaneous
 * queue timeout check and redirect it to leave_queue step
 * handle process
 *
 * @access public
 * @param  int $log_id
 * @param  int $queue_id
 * @return void
 */
router.route('/leave_queue_redirect/:log_id/:queue_id')
  .get(function (req, res) {
    var redirecturl = `${config.call_url}twilio/process_next_step/${req.params.log_id}?leave_queue=1&amp;queue_id=${req.params.queue_id}`;
    res.set('Content-Type', 'text/xml');
    res.send(`<?xml version="1.0" encoding="UTF-8"?>
        <Response>
        <Redirect method="POST">${redirecturl}</Redirect>
        </Response>`);
  })
  .post(function (req, res) {
    var redirecturl = `${config.call_url}twilio/process_next_step/${req.params.log_id}?leave_queue=1&amp;queue_id=${req.params.queue_id}`;
    res.set('Content-Type', 'text/xml');
    res.send(`<?xml version="1.0" encoding="UTF-8"?>
        <Response>
        <Redirect method="POST">${redirecturl}</Redirect>
        </Response>`);
  })

/*
 * Add customer on hold
 */
router.route('/put_hold/:account_sid/:task_sid/:hold')
  .get(async function (req, resp) {

    let acct = await DynamoHelper.getAccountDetail(req.params.account_sid);
    const twilio = new Twilio(acct.account_sid, acct.authtoken);
    const workspace = twilio.taskrouter.workspaces(acct.workspace_sid)

    try {

      const callTask = await workspace.tasks(req.params.task_sid).fetch();
      const callTaskAttr = JSON.parse(callTask.attributes);

      const conferences = await twilio.conferences.list({
        friendlyName: req.params.task_sid,
        status: 'in-progress',
      });

      if (conferences.length) {

        let customerCallSid = '';
        if (callTaskAttr.taskchannel == 'custom1') {
          customerCallSid = callTaskAttr.outboundCustomerCallSid;
        } else {
          customerCallSid = callTaskAttr.call_sid;
        }

        const customerParticipants = await twilio.conferences(conferences[0].sid)
          .participants(customerCallSid)
          .fetch();

        let holdCall = false;
        if (req.params.hold == 'true') {
          holdCall = true;
        }

        if (!customerParticipants.length) {
          const cpapiClient = require('../libraries/cpapi-client');
          const mccClient = new cpapiClient.mccClient(config.db.serviceTokens.readWrite);
      
          let callCenterSetting = await mccClient.cache.getData(`callcenter/${acct.id}`);
          let holdUrl = callCenterSetting.hold_music_url ? (config.CP_CDN_URL.slice(0, -1) + callCenterSetting.hold_music_url) : callCenterSetting.default_hold_music_url;

          await customerParticipants.update({hold: holdCall, holdUrl: holdUrl})
        }

        resp.status(200);
        resp.set('Content-Type', 'application/json');
        resp.send({});
      } else {
        // console.log('put_hold Conference not found, Conference already ended');
        resp.status(400);
        resp.set('Content-Type', 'application/json');
        resp.send({
          'error': 'Conference not found, Conference already ended'
        });
      }
    } catch (e) {
      console.error(e, new Error().stack);

      if (e.status == '404') {
        resp.status(422);
      } else {
        resp.status(400);
      }

      resp.set('Content-Type', 'application/json');
      resp.send({
        'error': e
      });
    }
  })

/*
 * Receive voip/phone call status for agent connected with voip/phone
 * Reject reservation on call reject/no-answer
 */
router.route('/voip_connect_status')
  .post(async function (req, resp) {
    let acct = await DynamoHelper.getAccountDetail(req.body.AccountSid);
    try {
      console.debug("/voip_connect_status", req);
      const twilio = new Twilio(acct.account_sid, acct.authtoken);
      const workspace = twilio.taskrouter.workspaces(acct.workspace_sid)

      if (['no-answer', 'busy', 'failed'].includes(req.body.CallStatus)) {
        let reservationUpdate = {reservationStatus: 'rejected'};

        const cpapiClient = require('../libraries/cpapi-client');
        const mccClient = new cpapiClient.mccClient(config.db.serviceTokens.readWrite);

        let callCenterSetting = await mccClient.cache.getData(`callcenter/${acct.id}`);
        if (['busy', 'failed'].includes(req.body.CallStatus) && callCenterSetting.on_reject_inactive && callCenterSetting.on_reject_inactive == 1) {
          const activitySid = (await workspace.activities.list({ friendlyName: 'Rejected' }))[0].sid;
          reservationUpdate.workerActivitySid = activitySid;
        }

        if (['no-answer'].includes(req.body.CallStatus) && callCenterSetting.on_cascade_inactive && callCenterSetting.on_cascade_inactive == 1) {
          reservationUpdate.workerActivitySid = (await workspace.activities.list({ friendlyName: 'No-Answer' }))[0].sid;
        }

        await workspace
          .tasks(req.query.taskSid)
          .reservations(req.query.reservationSid)
          .update(reservationUpdate)
          .catch((e) => {
            // 410 error is for trying to reject reservation which is already rescinded
            if (e.status !== 410) {
              console.error(e, new Error().stack);
            }
          });
      }

      return resp.type('xml').send('<Response>Success</Response>');
    } catch (e) {
      console.error(e, new Error().stack);
      return resp.type('xml').send('<Response>Success</Response>');
    }
  })

/**
 * Send voice mail email, This will be called as async lamdba function invocation
 */
router.route('/send_email_async')
  .post(async function (req, resp) {

    try {
      const { getCurrentInvoke } = require('@vendia/serverless-express');
      const currentInvoke = getCurrentInvoke();
      let CommonMethod = require('../libraries/common-methods').CommonMethod;
      let miscellaneous = require('../config/miscellaneous');
      let common_method = new CommonMethod();

      let {
        call,
        duration,
        emails
      } = currentInvoke.event.asyncBody;

      var parse_params = {
        'call_number': call['call_number'],
        'number_called': call['call_name'],
        'location_name': common_method.escapeTwilioChars(call['location_name']),
        'ad_name': common_method.escapeTwilioChars(call['ad_name']),
        'date_received': moment.tz(config.TZ).format("YYYY-MM-DD"),
        'name': common_method.escapeTwilioChars(call['customer_name']),
        'caller_type': call['customer_type'],
        'duration': duration ? duration : 0,
        'recording_url': config.API_CALL_URL + '/recording/' + call['twilio_id']
      };

      var msg = `
        <!DOCTYPE html>
        <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width">
            </head>
            <body>
                <img style="width: 188px;" src="${config.site_url + 'images/cp_big_logo.jpg'}">
                <br /><br />
                You have a new Voicemail Message:<br /><br />

                <table>
                    <thead>
                    </thead>
                    <tbody>
                        <tr><td>Location:</td><td>${parse_params.location_name}</td></tr>
                        <tr><td>From:</td><td>${parse_params.call_number}</td></tr>
                        <tr><td>To:</td><td>${parse_params.number_called} - ${parse_params.ad_name}</td></tr>
                        <tr><td>Received:</td><td>${parse_params.date_received}</td></tr>
                        <tr><td>Name:</td><td>${parse_params.name || 'N/A'}</td></tr>
                        <tr><td>Caller Type:</td><td>${parse_params.caller_type || 'N/A'}</td></tr>
                        <tr><td>Length:</td><td>${moment.utc(parseInt(parse_params.duration)*1000).format('HH:mm:ss')}</td></tr>
                        <tr><td colspan="2">&nbsp;</td></tr>
                        <tr><td>Listen:</td><td><a href="${parse_params.recording_url}">Recording</a></td></tr>
                    </tbody>
                </table>
            </body>
        </html>
        `
      common_method.send_email_notification(
        msg,
        'Voicemail message',
        { 'to_email': emails },
        {
          'email': miscellaneous.EMAIL_DETAILS.VOICEMAIL_EMAIL,
          'name': 'CallPotential'
        }
      ).then(function() {
        resp.status(200);
        resp.set('Content-Type', 'application/json');
        resp.send();
      })
    } catch(e) {
      console.error(e, new Error().stack);
      resp.status(200);
      resp.set('Content-Type', 'application/json');
      resp.send();
    }
  })
  
router.route('/calldetail/:log_id')
.post(async function (req, resp) {
  try {
    let { body } = req;
    if (!body) {
      resp.status(400);
      resp.set('Content-Type', 'application/json');
      resp.send({
        'error': 'Request body is missing'
      });
      return; 
    }

    // JSON Parse the body
    if (typeof body === 'string') {
      body = JSON.parse(body);
    }
    const callDetail = await CallDetail.updateCallDetail(body);

    resp.status(200);
    resp.set('Content-Type', 'application/json');
    resp.send(callDetail);
  } catch (e) {
    console.error(e.stack);
    resp.status(400);
    resp.set('Content-Type', 'application/json');
    resp.send({
      'error': e.message
    });
  }
});

/**
 * This route is used by Twilio when we are making an outbound call to a contact.
 * We have to do this, instead of adding the outbound call leg to the contact as a conference
 * participant, because there is a chance that we need to announce something to the caller.
 * For example, when an outbound call is being recorded, we need to announce that 
 * the call may be recorded.  If we added the outbound call leg to the conference as a participant,
 * then we would not be able to announce anything to the caller prior to them joining the conference.
 * 
 * This route expects that the querystring will contain a key, "argsBase64", 
 * whose value is a base64 encoded string that represents a JSON string that, when parsed, will
 * describe an object with the following shape:
 * 
 * {
 *   announce: string, // text to be announced to the caller. if record_outgoing is true, this will have a value
 *   confOpts: object, // the full conference options including callbacks
 *   confName: string  // the friendly name of the conference
 * }
 * 
 * Upon receipt of the above args, this route will return TwiML that will connect the outbound call
 * to the conference via a <Dial><Conference> verb.
 */
router.route('/outbound-call-connect')
  .post(async function (req, resp) {
    const { argsBase64 } = req.query;
    if (argsBase64) {
      const argsText = Buffer.from(argsBase64, 'base64').toString('utf-8');
      const args = JSON.parse(argsText);

      const customerDialTwiml = new Twilio.twiml.VoiceResponse();
      if (args.announce) {
        customerDialTwiml.say(args.announce);
      }
      customerDialTwiml.dial().conference(args.confOpts, args.confName);
      return resp.type('xml').send(customerDialTwiml.toString());
    } else {
      return resp.status(400).send('Invalid parameters');
    }
  });

module.exports = router