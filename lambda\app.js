'use strict'
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const config = require('./config/config');
const app = express();

app.use(cors())
app.use(bodyParser.json())
app.use(bodyParser.urlencoded({
  extended: true
}))
var allowCrossDomain = function(req, res, next) {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,PATCH');
  res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization');
  next()
};
app.use(allowCrossDomain);
app.use(bodyParser.json({
  type: 'application/vnd.api+json'
}));

var env_prepend_url = config.env_url ? config.env_url : '';
var prepend_url = config.env_prefix_url ? config.env_prefix_url : '';

app.get('/', (req, res) => {
  res.json({
    "index": "1"
  });
})

app.use(env_prepend_url + '/twilio', require('./routes/twillio.js'));
app.use(prepend_url + '/twilio', require('./routes/twillio.js'));

app.use(env_prepend_url + '/location_call', require('./routes/location_call.js'));
app.use(prepend_url + '/location_call', require('./routes/location_call.js'));

app.use(env_prepend_url + '/pay_by_phone', require('./routes/pay_by_phone.js'));
app.use(prepend_url + '/pay_by_phone', require('./routes/pay_by_phone.js'));

app.use(env_prepend_url + '/collection_call', require('./routes/collection_call.js'));
app.use(prepend_url + '/collection_call', require('./routes/collection_call.js'));

app.use(env_prepend_url + '/validate_agent_number', require('./routes/validate_agent_number.js'));
app.use(prepend_url + '/validate_agent_number', require('./routes/validate_agent_number.js'));

app.use(env_prepend_url + '/video', require('./routes/video_call.js'));
app.use(prepend_url + '/video', require('./routes/video_call.js'));

process.on('uncaughtException', function(err) {
  console.error(err, new Error().stack);
});

// Export your express server so you can import it in the lambda function.
module.exports = app
