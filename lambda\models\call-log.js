'use strict';

const _ = require('lodash');
var main_config = require('../config/config');
var acctTableDynamo = main_config.dynamodb.acctTable;
var callTableDynamo = main_config.dynamodb.callTable;
const AWS = require("aws-sdk");
const moment = require('moment-timezone');
const { convIntelClient } = require('../libraries/conv-intel');
const cpapiClient = require('../libraries/cpapi-client');
const bugsnagService = require('../libraries/bugsnag-service');
const dynamo = new AWS.DynamoDB.DocumentClient({ endpoint: main_config.dynamodb.endpoint });

Array.prototype.forEachAsync = async function (fn) { 
  for (let t of this) { await fn(t) }
}

var CallLogModel = class CallLogModel {
  async list_dynamodb(filterValue, filterKey='log_id') {
    bugsnagService.leaveBreadcrumb('Starting DynamoDB query', {
      filterValue,
      filterKey,
      tableName: callTableDynamo
    });

    let params = {
      TableName: callTableDynamo,
    };

    if (filterKey == 'twilio_id') {
      params['KeyConditionExpression'] = 'twilio_id = :twilio_id';
      params['ExpressionAttributeValues'] = { ':twilio_id': filterValue };
    } else {
      params['IndexName'] = 'log_id-index';
      params['KeyConditionExpression'] = 'log_id = :log_id';
      params['ExpressionAttributeValues'] = { ':log_id': parseInt(filterValue, 10) };
    }

    try {
      const data = await dynamo.query(params).promise();
      bugsnagService.leaveBreadcrumb('DynamoDB query successful', {
        itemsFound: data.Items.length,
        filterValue,
        filterKey
      });

      if (data.Items.length === 0) return {};

      return data.Items[0];
    } catch (e) {
      console.error('Error querying dynamo:', e, new Error().stack, filterValue, filterKey);
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          dynamodb: {
            filterValue,
            filterKey,
            tableName: callTableDynamo,
            params
          }
        }
      });
      return {};
    }
  }

  async put_dynamodb(log_info) {
    bugsnagService.leaveBreadcrumb('Starting put_dynamodb operation', {
      hasLogInfo: !!log_info,
      logInfoKeys: log_info ? Object.keys(log_info) : []
    });

    const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
    try {
      log_info['timetolive'] = moment().add('2', 'days').unix();
    } catch (e) {
      console.error(e, new Error().stack);
      bugsnagService.notify(e, {
        severity: 'warning',
        metadata: {
          operation: 'put_dynamodb_timetolive',
          logInfo: log_info
        }
      });
    }

    try {
      const result = await callClient.postData(`call`, log_info);
      bugsnagService.leaveBreadcrumb('put_dynamodb operation successful', {
        resultExists: !!result
      });
      return result;
    } catch (e) {
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'put_dynamodb_postData',
          logInfo: log_info
        }
      });
      throw e;
    }
  }

  async get_account_dynamo(account_id) {
    bugsnagService.leaveBreadcrumb('Starting get_account_dynamo operation', {
      account_id,
      tableName: acctTableDynamo
    });

    const params = {
      TableName: acctTableDynamo,
      KeyConditionExpression: 'id = :account_id',
      ExpressionAttributeValues: { ':account_id': account_id },
    };

    const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: main_config.dynamodb.endpoint});
    try {
      const data = await dynamo.query(params).promise();
      bugsnagService.leaveBreadcrumb('get_account_dynamo query successful', {
        account_id,
        itemsFound: data.Items.length
      });

      if (data.Items.length === 0) return {};

      return data.Items[0];
    } catch (e) {
      console.error('Error querying dynamo:', e, new Error().stack);
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'get_account_dynamo',
          account_id,
          tableName: acctTableDynamo,
          params
        }
      });
      return {};
    }
  }

  get_next_step(step_detail) {
    bugsnagService.leaveBreadcrumb('Processing get_next_step', {
      stepType: step_detail?.type,
      hasChildren: !!(step_detail?.children && step_detail.children.length > 0)
    });

    switch (step_detail.type) {
      case 'route_connect':
        if (step_detail.children && step_detail.children.length > 0) {
          bugsnagService.leaveBreadcrumb('Returning first child step', {
            stepType: step_detail.type,
            childrenCount: step_detail.children.length
          });
          return step_detail.children[0];
        }
        return [];
      default:
        bugsnagService.leaveBreadcrumb('Unknown step type, returning empty array', {
          stepType: step_detail.type
        });
        return [];
    }
  }

  async queue_save_next_step(log_detail) {
    bugsnagService.leaveBreadcrumb('Starting queue_save_next_step', {
      hasLogDetail: !!log_detail,
      hasCurrentRouteStep: !!(log_detail?.current_route_step)
    });

    var next_step = this.get_next_step(log_detail.current_route_step);
    var log_info = {
      'current_route_step': JSON.stringify(next_step)
    };
    var cust_para_list = ['twilio_id', 'location_id'];
    if (!next_step || (next_step && next_step.length === 0)) {
      log_info['is_route_complete'] = 1;
      cust_para_list.push('log_id');
      bugsnagService.leaveBreadcrumb('Route marked as complete', {
        nextStepEmpty: !next_step || next_step.length === 0
      });
    }

    var log_conds = _.pick(log_detail, cust_para_list);
    await this.update_dynamodb(log_conds, log_info)
      .catch(e => {
        console.error(e, new Error().stack);
        bugsnagService.notify(e, {
          severity: 'error',
          metadata: {
            operation: 'queue_save_next_step_update_dynamodb',
            log_conds,
            log_info
          }
        });
      });

    bugsnagService.leaveBreadcrumb('queue_save_next_step completed', {
      nextStepExists: !!next_step
    });
    return next_step;
  }

  async update_dynamodb(call_conds, call_info, no_db_save) {
    bugsnagService.leaveBreadcrumb('Starting update_dynamodb', {
      hasCallConds: !!call_conds,
      hasCallInfo: !!call_info,
      no_db_save: !!no_db_save,
      callCondsKeys: call_conds ? Object.keys(call_conds) : [],
      callInfoKeys: call_info ? Object.keys(call_info) : []
    });

    let res = null, err = null;

    if ('duration' in call_info) {
      call_info['call_duration'] = call_info['duration'];
      delete call_info['duration'];
      bugsnagService.leaveBreadcrumb('Converted duration to call_duration');
    }

    if (!no_db_save) {
      no_db_save = false
    }

    if (call_conds.log_id) {
      bugsnagService.leaveBreadcrumb('Fetching call conditions by log_id', {
        log_id: call_conds.log_id
      });
      call_conds = await this.list_dynamodb(call_conds.log_id);
    }

    try {
      const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
      await callClient
        .putData(`call/${call_conds.location_id}/${call_conds.twilio_id}`, call_info);
      bugsnagService.leaveBreadcrumb('Call data updated successfully', {
        location_id: call_conds.location_id,
        twilio_id: call_conds.twilio_id
      });
    } catch (e) {
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'update_dynamodb_putData',
          call_conds,
          call_info
        }
      });
      throw e;
    }

    let log_info = await this.list_dynamodb(call_conds.twilio_id, 'twilio_id');

    let ciEnabled = false;
    try {
      ciEnabled = await convIntelClient.checkFeatureToggle(log_info.account_id);
      bugsnagService.leaveBreadcrumb('Conv intel feature toggle checked', {
        account_id: log_info.account_id,
        ciEnabled
      });
    } catch (e) {
      console.log('Error in calling conv intel api: ', e, new Error().stack);
      bugsnagService.notify(e, {
        severity: 'warning',
        metadata: {
          operation: 'update_dynamodb_convIntelCheck',
          account_id: log_info.account_id
        }
      });
    }

    try {
      if (call_info && call_info.is_route_complete === 1 && no_db_save === false && !log_info.db_log_id) {
        bugsnagService.leaveBreadcrumb('Route complete, saving call log to database', {
          ciEnabled,
          has_db_log_id: !!log_info.db_log_id
        });

        if (ciEnabled) {
          delete log_info['recording_url'];
          bugsnagService.leaveBreadcrumb('Recording URL removed due to conv intel enabled');
        }
        return await this.save_call_log_from_dynamo_db(log_info);
      } else if (call_info.is_route_complete === 1 && no_db_save === false && log_info.db_log_id) {
        // Handle case where MySQL record already exists but needs updating (e.g., recording URL added later)
        bugsnagService.leaveBreadcrumb('MySQL record exists, checking for updates needed', {
          db_log_id: log_info.db_log_id,
          has_recording_url: !!log_info.recording_url,
          has_recording_sid: !!log_info.recording_sid,
          ciEnabled
        });

        // Check if we have recording data that might need to be updated in MySQL
        if (log_info.recording_url || log_info.recording_sid) {
          let updateData = {};

          if (log_info.recording_url && !ciEnabled) {
            updateData.recording_url = log_info.recording_url;
          }
          if (log_info.recording_sid) {
            updateData.recording_sid = log_info.recording_sid;
          }
          if (log_info.call_duration) {
            updateData.duration = log_info.call_duration;
          }

          if (Object.keys(updateData).length > 0) {
            bugsnagService.leaveBreadcrumb('Updating existing MySQL record with recording data', {
              db_log_id: log_info.db_log_id,
              updateData: updateData
            });

            try {
              const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
              await callClient.putData(`calldetail/${log_info.db_log_id}`, updateData);

              bugsnagService.leaveBreadcrumb('MySQL record updated successfully', {
                db_log_id: log_info.db_log_id,
                updateData: updateData
              });
            } catch (updateError) {
              bugsnagService.notify(updateError, {
                severity: 'error',
                metadata: {
                  operation: 'update_existing_mysql_record',
                  db_log_id: log_info.db_log_id,
                  updateData: updateData,
                  twilio_id: log_info.twilio_id
                }
              });
            }
          }
        }
      }
    } catch (e) {
      err = e;
      console.log(e, new Error().stack);
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'update_dynamodb_save_call_log',
          call_info,
          log_info,
          ciEnabled
        }
      });
    }

    bugsnagService.leaveBreadcrumb('update_dynamodb completed', {
      hasError: !!err,
      hasResult: !!res
    });
    return [res, err];
  }

  async create_call_log(log_info) {
    bugsnagService.leaveBreadcrumb('Starting create_call_log', {
      hasLogInfo: !!log_info,
      logInfoKeys: log_info ? Object.keys(log_info) : []
    });

    delete(log_info['log_id']);

    try {
      const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
      const result = await callClient.postData('calldetail', log_info); // create
      bugsnagService.leaveBreadcrumb('Call log created successfully', {
        resultExists: !!result,
        twilio_id: log_info.twilio_id
      });
      return result;
    } catch (e) {
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'create_call_log',
          log_info
        }
      });
      throw new Error('Something went wrong not able to create a calldetail record.');
    }
  }

  async update_call_log(log_info) {
    bugsnagService.leaveBreadcrumb('Starting update_call_log', {
      hasLogInfo: !!log_info,
      twilio_id: log_info?.twilio_id,
      logInfoKeys: log_info ? Object.keys(log_info) : []
    });

    delete(log_info['log_id']);

    try {
      const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
      const result = await callClient.putData('calldetail/' + log_info['twilio_id'], log_info); // update
      bugsnagService.leaveBreadcrumb('Call log updated successfully', {
        twilio_id: log_info['twilio_id'],
        resultExists: !!result
      });
      return result;
    } catch (e) {
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'update_call_log',
          twilio_id: log_info['twilio_id'],
          log_info
        }
      });
      throw new Error('Something went wrong not able to update a calldetail record.');
    }
  }

  async save_call_log_from_dynamo_db(log_info) {
    bugsnagService.leaveBreadcrumb('Starting save_call_log_from_dynamo_db', {
      hasLogInfo: !!log_info,
      dynamo_log_id: log_info?.log_id,
      db_log_id: log_info?.db_log_id,
      logInfoKeys: log_info ? Object.keys(log_info) : []
    });

    if ('call_duration' in log_info) {
      log_info['duration'] = log_info['call_duration'];
      delete log_info['call_duration'];
      bugsnagService.leaveBreadcrumb('Converted call_duration to duration');
    }

    let dynamo_log_id = log_info.log_id;
    let db_log_id = log_info.db_log_id;

    if (log_info['lead_id']) {
      log_info['fk_lead_id'] = log_info['lead_id'];
      bugsnagService.leaveBreadcrumb('Set fk_lead_id from lead_id', {
        lead_id: log_info['lead_id']
      });
    }

    // Delete fields which are not in mysql call_logs
    delete log_info['db_log_id'];
    delete log_info['log_id'];
    delete log_info['account_id'];
    delete log_info['timetolive'];
    delete log_info['es_tenant_id'];
    delete log_info['es_lead_id'];
    // delete log_info['billingDuration'];
    delete log_info['call_duration_sid'];
    delete log_info['lead_id'];

    bugsnagService.leaveBreadcrumb('Cleaned log_info for MySQL', {
      remainingKeys: Object.keys(log_info)
    });

    try {
      let mysqlLog = await this.update_call_log(log_info); // update call log record
      db_log_id = mysqlLog.log_id;
      bugsnagService.leaveBreadcrumb('MySQL call log updated', {
      db_log_id,
      dynamo_log_id
      });
    } catch (e) {
      bugsnagService.notify(e, {
      severity: 'error',
      metadata: {
        operation: 'save_call_log_from_dynamo_db_update_mysql',
        dynamo_log_id,
        log_info: _.omit(log_info, ['current_route_step']) 
      }
      });
    }


    try {
      await new CallLogModel().update_dynamodb(
        {'location_id': log_info.location_id, 'twilio_id': log_info.twilio_id},
        {db_log_id, channel : log_info.channel},
        true
      );
      bugsnagService.leaveBreadcrumb('DynamoDB updated with db_log_id', {
        db_log_id,
        location_id: log_info.location_id,
        twilio_id: log_info.twilio_id
      });
    } catch (e) {
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'save_call_log_update_dynamodb_with_db_log_id',
          db_log_id,
          location_id: log_info.location_id,
          twilio_id: log_info.twilio_id
        }
      });
    }

    try {
      const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
      let taskList = await callClient.getData(`trackcallcentertasks?filterLog_id=${dynamo_log_id}`);
      if (taskList && taskList.items && taskList.items.length > 0) {
        bugsnagService.leaveBreadcrumb('Updating track_callcenter_tasks records', {
          taskCount: taskList.items.length,
          dynamo_log_id,
          db_log_id
        });

        await taskList.items.forEachAsync(async (element) => {
          element.log_id = db_log_id
          await callClient.putData(`trackcallcentertasks/${element.id}`, element);
        });

        bugsnagService.leaveBreadcrumb('All track_callcenter_tasks updated successfully');
      }
    } catch (e) {
      console.error('Error in updateing track_callcenter_tasks record', e, new Error().stack);
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'save_call_log_update_track_callcenter_tasks',
          dynamo_log_id,
          db_log_id
        }
      });
    }

    if (log_info['fk_lead_id']) {
      bugsnagService.leaveBreadcrumb('Lead ID found but lead update is commented out', {
        fk_lead_id: log_info['fk_lead_id'],
        dynamo_log_id,
        db_log_id
      });
      /*const intClient = new cpapiClient.intClient(main_config.db.serviceTokens.readWrite);
      // Todo: This endpoint expects lead ES id
      let leadData = await intClient.getData(`lead/${log_info['fk_lead_id']}`);
      if (leadData && leadData.log_id == dynamo_log_id) {
        leadData.log_id = db_log_id;
        await intClient.putData('lead', leadData);
      }*/
    }

    bugsnagService.leaveBreadcrumb('save_call_log_from_dynamo_db completed successfully', {
      dynamo_log_id,
      db_log_id
    });
  }
};

module.exports = CallLogModel
