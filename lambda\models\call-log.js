'use strict';

const _ = require('lodash');
var main_config = require('../config/config');
var acctTableDynamo = main_config.dynamodb.acctTable;
var callTableDynamo = main_config.dynamodb.callTable;
const AWS = require("aws-sdk");
const moment = require('moment-timezone');
const { convIntelClient } = require('../libraries/conv-intel');
const cpapiClient = require('../libraries/cpapi-client');
const dynamo = new AWS.DynamoDB.DocumentClient({ endpoint: main_config.dynamodb.endpoint });

Array.prototype.forEachAsync = async function (fn) { 
  for (let t of this) { await fn(t) }
}

var CallLogModel = class CallLogModel {
  async list_dynamodb(filterValue, filterKey='log_id') {
    let params = {
      TableName: callTableDynamo,
    };

    if (filterKey == 'twilio_id') {
      params['KeyConditionExpression'] = 'twilio_id = :twilio_id';
      params['ExpressionAttributeValues'] = { ':twilio_id': filterValue };
    } else {
      params['IndexName'] = 'log_id-index';
      params['KeyConditionExpression'] = 'log_id = :log_id';
      params['ExpressionAttributeValues'] = { ':log_id': parseInt(filterValue, 10) };
    }

    try {
      const data = await dynamo.query(params).promise();
      if (data.Items.length === 0) return {};

      return data.Items[0];
    } catch (e) {
      console.error('Error querying dynamo:', e, new Error().stack, filterValue, filterKey);
      return {};
    }
  }

  async put_dynamodb(log_info) {
    const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
    try {
      log_info['timetolive'] = moment().add('2', 'days').unix();
    } catch (e) {
      console.error(e, new Error().stack)
    }
    return await callClient
      .postData(`call`, log_info);
  }

  async get_account_dynamo(account_id) {
    const params = {
      TableName: acctTableDynamo,
      KeyConditionExpression: 'id = :account_id',
      ExpressionAttributeValues: { ':account_id': account_id },
    };

    const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: main_config.dynamodb.endpoint});
    try {
      const data = await dynamo.query(params).promise();
      if (data.Items.length === 0) return {};

      return data.Items[0];
    } catch (e) {
      console.error('Error querying dynamo:', e, new Error().stack);
      return {};
    }
  }

  get_next_step(step_detail) {
    switch (step_detail.type) {
      case 'route_connect':
        if (step_detail.children && step_detail.children.length > 0)
          return step_detail.children[0];
        return [];
      default:
        return [];
    }
  }

  async queue_save_next_step(log_detail) {
    var next_step = this.get_next_step(log_detail.current_route_step);
    var log_info = {
      'current_route_step': JSON.stringify(next_step)
    };
    var cust_para_list = ['twilio_id', 'location_id'];
    if (!next_step || (next_step && next_step.length === 0)) {
      log_info['is_route_complete'] = 1;
      cust_para_list.push('log_id');
    }

    var log_conds = _.pick(log_detail, cust_para_list);
    await this.update_dynamodb(log_conds, log_info)
      .catch(e => console.error(e, new Error().stack))
    return next_step;
  }

  async update_dynamodb(call_conds, call_info, no_db_save) {
    let res = null, err = null;

    if ('duration' in call_info) {
      call_info['call_duration'] = call_info['duration'];
      delete call_info['duration'];
    }

    if (!no_db_save) {
      no_db_save = false
    }

    if (call_conds.log_id) {
      call_conds = await this.list_dynamodb(call_conds.log_id);
    }

    const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
    await callClient
      .putData(`call/${call_conds.location_id}/${call_conds.twilio_id}`, call_info);

    let log_info = await this.list_dynamodb(call_conds.twilio_id, 'twilio_id');

    let ciEnabled = false;
    try {
      ciEnabled = await convIntelClient.checkFeatureToggle(log_info.account_id);
    } catch (e) {
      console.log('Error in calling conv intel api: ', e, new Error().stack);
    }

    try {
      if (call_info.is_route_complete === 1 && no_db_save === false && !log_info.db_log_id) {
        if (ciEnabled) {
          delete log_info['recording_url'];
        }
        return await this.save_call_log_from_dynamo_db(log_info);
      }
    } catch (e) {
      err = e;
      console.log(e, new Error().stack);
    }

    return [res, err];
  }

  async create_call_log(log_info) {
    delete(log_info['log_id']);

    try {
      const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
      return await callClient.postData('calldetail', log_info); // create
    } catch (e) {
      throw new Error('Something went wrong not able to create a calldetail record.');
    }
  }

  async update_call_log(log_info) {
    delete(log_info['log_id']);

    try {
      const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
      return await callClient.putData('calldetail/' + log_info['twilio_id'], log_info); // update
    } catch (e) {
      throw new Error('Something went wrong not able to update a calldetail record.');
    }
  }

  async save_call_log_from_dynamo_db(log_info) {
    if ('call_duration' in log_info) {
      log_info['duration'] = log_info['call_duration'];
      delete log_info['call_duration'];
    }

    let dynamo_log_id = log_info.log_id;
    let db_log_id = log_info.db_log_id;

    if (log_info['lead_id']) {
      log_info['fk_lead_id'] = log_info['lead_id'];
    }

    // Delete fields which are not in mysql call_logs
    delete log_info['db_log_id'];
    delete log_info['log_id'];
    delete log_info['account_id'];
    delete log_info['timetolive'];
    delete log_info['es_tenant_id'];
    delete log_info['es_lead_id'];
    // delete log_info['billingDuration'];
    delete log_info['call_duration_sid'];
    delete log_info['lead_id'];

    const callClient = new cpapiClient.callClient(main_config.db.serviceTokens.readWrite);
    let mysqlLog = await this.update_call_log(log_info); // update call log record
    db_log_id = mysqlLog.log_id;

   
    await new CallLogModel().update_dynamodb(
      {'location_id': log_info.location_id, 'twilio_id': log_info.twilio_id},
      {db_log_id, channel : log_info.channel},
      true
    )

    try {
      let taskList = await callClient.getData(`trackcallcentertasks?filterLog_id=${dynamo_log_id}`);
      if (taskList && taskList.items && taskList.items.length > 0) {
        await taskList.items.forEachAsync(async (element) => {
          element.log_id = db_log_id
          await callClient.putData(`trackcallcentertasks/${element.id}`, element);
        });
      }
    } catch (e) {
      console.error('Error in updateing track_callcenter_tasks record', e, new Error().stack);
    }

    if (log_info['fk_lead_id']) {
      /*const intClient = new cpapiClient.intClient(main_config.db.serviceTokens.readWrite);
      // Todo: This endpoint expects lead ES id
      let leadData = await intClient.getData(`lead/${log_info['fk_lead_id']}`);
      if (leadData && leadData.log_id == dynamo_log_id) {
        leadData.log_id = db_log_id;
        await intClient.putData('lead', leadData);
      }*/
    }
  }
};

module.exports = CallLogModel
