'use strict'
const serverlessExpress = require('@vendia/serverless-express')
const app = require('./app')
const {requestMapper, responseMapper} = require('./serverless-express-requestmapper')

exports.handler = function(event, context) {

  var event_call_type = 'aws_api_gateway';
  if (event.resource === '/{proxy+}') {
    event_call_type = 'aws_api_gateway';
  } else if (event.source === 'aws.events') {
    // keep warm!
    no_op(event, context);
    return;
  }

  if (event.asyncInvoke) {
    console.log('asyncInvoke', event);
  }

  switch (event_call_type) {
    case 'aws_api_gateway':
      return serverlessExpress({
                app,
                eventSource: {
                  getRequest: requestMapper,
                  getResponse: responseMapper
                }
              })(event, context);
    default:
      context.done(null, 'No Matching event type case found');
  }
};

/* eslint-disable no-unused-vars */
function no_op(event, context) {
  // Load frequently used packages so they get cached
  const AWS = require('aws-sdk');
  const express = require('express');
  const Twilio = require('twilio');
  const moment = require('moment-timezone');
  context.done(null);
}
/* eslint-enable no-unused-vars */

exports.no_op = no_op;
