const config = require("../../config/config");
const Twilio = require('twilio');

async function createAgentOutboundCall(
  twilio,
  agentConferenceParams,
  TaskSid,
  ReservationSid,
  to,
  from,
) {

  /**
   * Create an outbound call using a <Dial><Conference> TwiML fragment and
   * the Twilio API calls.create method.
   * 
   * A Status callback is specified that will monitor the call for "busy" or "no-answer"
   * and reject the active task reservation.
   */
  const twiml = new Twilio.twiml.VoiceResponse();
  const dial = twiml.dial();

  dial.conference(agentConferenceParams, TaskSid);

  console.debug("Creating agent outbound call", twilio, agentConferenceParams, TaskSid, ReservationSid);
  return await twilio.calls.create({
    twiml: twiml.toString(),
    statusCallbackEvent: ["answered", "completed"],
    statusCallback: `${config.call_url}twilio/voip_connect_status?taskSid=${TaskSid}&reservationSid=${ReservationSid}`,
    to,
    from,
  });
}

/**
 * NOTE: This function is duplicated in two places inside of this project.  The reason for this
 * is because the resulting lambda functions (twilio, conference-event) deployed from this project 
 * use different root folders and there is no way to share code between them.
 * 
 * This function is called by the `twilio` lambda function.
 * 
 * TODO: As we continue to reorganize this project, we will structure common code so that it can
 * be used by both lambda functions
 */
async function updateReservationStatus(twilioClient, workspace, taskSid, reservationSid, agentCallSid) {

  return await workspace
    .tasks(taskSid)
    .reservations(reservationSid)
    .update({ reservationStatus: 'accepted' })
    .catch(async (error) => {
      console.log(error, new Error().stack);
      // If error in accepting reservation, end the agent call
      await updateCallStatus(
        twilioClient,
        agentCallSid,
        'completed'
      );
    });
}

async function updateCallStatus(twilioClient, callSid, status) {
  try {
    
    await twilioClient
    .calls(callSid)
    .update({ status });

  } catch (e) {
    console.log(e, new Error().stack);
  }
}

/**
 * NOTE: This function is duplicated in two places inside of this project.  The reason for this
 * is because the resulting lambda functions (twilio, conference-event) deployed from this project 
 * use different root folders and there is no way to share code between them.
 * 
 * This function is called by the `twilio` lambda function.
 * 
 * TODO: As we continue to reorganize this project, we will structure common code so that it can
 * be used by both lambda functions
 */
async function dialCustomerInConference(twilioClient, customerCallSid, conferenceFriendlyName) {

  const conferenceParams = {
    participantLabel: "customer",
    startConferenceOnEnter: true,
    endConferenceOnExit: true,
  };

  const confTwiml = new Twilio.twiml.VoiceResponse();
  const dial = confTwiml.dial();
  dial.conference(conferenceParams, conferenceFriendlyName);

  try {
    console.debug("Dial customer in conference", customerCallSid, conferenceFriendlyName);
    await twilioClient
    .calls(customerCallSid)
    .update({
      twiml: confTwiml.toString(),
    });

  } catch(e) {
    console.log(e, new Error().stack);
  }

}

async function handleVoiceChannelTimeout(redisClient, twilio, data) {
  let taskAttributes = JSON.parse(data.TaskAttributes); 
  const workerAttributes = JSON.parse(data.WorkerAttributes);

  const taskchannel = taskAttributes.taskchannel
    ? taskAttributes.taskchannel.toLowerCase()
    : "";
  const contactURI = workerAttributes.contact_uri
    ? workerAttributes.contact_uri.toLowerCase()
    : "";

  if (taskchannel === "voice" && !contactURI.includes("client:agent")) {
    let call_key = "voipPhoneSid-" + data.ReservationSid;
    const callSid = await redisClient.get(call_key);
    console.log("RESERVATION TIMEOUT", data, callSid);
    if (callSid) {
      try {
        await twilio
          .calls(callSid)
          .update({ status: "completed" });
      } catch (e) {
        console.error(e, new Error().stack);
      }
    }
  }
}

async function handleVoiceChannelCancellation(redisClient, twilio, data) {
  let taskAttributes = JSON.parse(data.TaskAttributes);
  const workerAttributes = JSON.parse(data.WorkerAttributes);

  const taskchannel = taskAttributes.taskchannel
    ? taskAttributes.taskchannel.toLowerCase()
    : "";
  const contactURI = workerAttributes.contact_uri
    ? workerAttributes.contact_uri.toLowerCase()
    : "";

  if (taskchannel === "voice" && !contactURI.includes("client:agent")) {
    let call_key = "voipPhoneSid-" + data.ReservationSid;
    const callSid = await redisClient.get(call_key);
    if (callSid) {
      try {
        await twilio
          .calls(callSid)
          .update({ status: "completed" });
      } catch (e) {
        console.error(e, new Error().stack);
      }
    }
  }
}

function isVoiceChannel(taskchannel) {
  return taskchannel ? taskchannel.toLowerCase() === "voice" : false;
}

function isCallbackChannel(taskchannel) {
  return taskchannel ? taskchannel.toLowerCase() === "callback" : false;
}

module.exports = {
  createAgentOutboundCall,
  dialCustomerInConference,
  updateReservationStatus,
  handleVoiceChannelCancellation,
  handleVoiceChannelTimeout,
  isVoiceChannel,
  isCallbackChannel
};