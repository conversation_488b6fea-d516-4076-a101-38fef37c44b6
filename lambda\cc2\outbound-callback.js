/**
 * outbound-callback
 */
const AWS = require('aws-sdk');
const Twilio = require('twilio');
const querystring = require('querystring');
const moment = require('moment-timezone');
const redis = require('redis');

const config = require('./config');
const cpapiClient = require('./cpapi_client');

/*
  CCC-215
  if the environment is NOT QA or CPT, remove the build number.
*/
let CONFIG_ENV = config.env;
if (config.qatest || (config.env.indexOf('qa') !== -1 && config.env.indexOf('cpt') !== -1)){
  CONFIG_ENV = config.env.split('-')[0];
}

const activity = {
  coolDown: 'Cool-Down',
  rejected: 'Rejected',
  onCall: 'On-call',
};

Array.prototype.forEachAsync = async function (fn) {
  for (let t of this) { await fn(t) }
}

async function handler(event, context, callback) {
  if (event.source === 'aws.events') return context.done(); // keep warm!

  // Merge get and post params of api gateway in params
  const params = Object.assign({}, event.queryStringParameters, querystring.parse(event.body));
  params.callType = params.callType || 'outbound'; // required for logs and conference interaction
  params.workflowStep = params.workflowStep || false;

  // console.log('OUTBOUND-CALLBACK ENTERED');
  // console.log('EVENT', event);
  // console.log('PARAMS', params);

  const locClient = new cpapiClient.locClient(params.authToken);
  const coreClient = new cpapiClient.coreClient(params.authToken);
  const twilioAccount = await twilioCredentials(coreClient);
  params.agentId = twilioAccount.userId;

  if (Object.keys(twilioAccount).length === 0) return callback(null, response(404));

  const twilio = new Twilio(twilioAccount.account_sid, twilioAccount.authtoken);
  const workspace = twilio.taskrouter.workspaces(twilioAccount.workspace_sid);

  if ('CallStatus' in params && params.CallStatus === 'completed') {
    try {
      const callClient = new cpapiClient.callClient(params.authToken);
      const callData = await callClient.getData(`call/${params.fromLocationId}/${params.CallSid}`);
      callData.billingDuration = params.CallDuration;

      await callClient.putData(`call/${params.fromLocationId}/${params.CallSid}`, callData);
    } catch (e) {
      console.error('Error updating call for billing duration', e);
    }

    try {
      const conferences = await twilio.conferences.list({
        friendlyName: params.Sid,
        status: 'in-progress',
      });

      if (conferences.length) {
        // console.log('conference participant fetch', conferences);
        const participants = await twilio.conferences(conferences[0].sid).participants.list();
        // a non started conference has zero participants
        if (!participants.length || participants.length == 1) {
          await workspace.tasks(params.Sid)
            .fetch()
            .then(async(task) => {
              let taskAttributes = JSON.parse(task.attributes);
              const transferCallStatus = taskAttributes.conference?.participants?.transfer?.status;
              if (!transferCallStatus || transferCallStatus !== 'confirmed') {
                await twilio.calls(taskAttributes.outboundCustomerCallSid).update({status: 'completed'});
              }
            });
        }
      }
    } catch (e) {
      console.error('Error ending outboundCustomerCallSid', e);
    }
    await markTaskAsCompleted(params, workspace);
  }

  // Stop the flow when connected by computer on call complete
  if ('CallStatus' in params && params.CallStatus === 'completed'
    && !['phone', 'location'].includes(params.connectType)) {
    return callback(null, response(200));
  }

  // Add workspace sid to params object
  params.workspace_sid = twilioAccount.workspace_sid;
  let voiceResponse = new Twilio.twiml.VoiceResponse();

  // Handle dialed call (who have CallStatus events) when completed or failed
  if (params.CallStatus) {
    // If a leg did not anwser or reject the call
    if (['failed', 'busy', 'no-answer'].includes(params.CallStatus)) {
      // console.log('markTaskAsCompleted', params);
      await markTaskAsCompleted(params, workspace);
      if (params.connectType === 'phone') {
        // console.log('updateWorkerActivity', activity.coolDown, params.WorkerSid);
        await updateWorkerActivity(activity.coolDown, params.WorkerSid, workspace);
      }
      return callback(null, response(200, voiceResponse.hangup()));
    }

    // If an agent hangup the call before joining the conference
    // complete the conference to avoid leaving the customer on wait
    if (params.CallStatus === 'completed') {

      // console.log('conferences fetch', {
      //   friendlyName: params.Sid,
      //   status: 'in-progress',
      // });
      const conferences = await twilio.conferences.list({
        friendlyName: params.Sid,
        status: 'in-progress',
      });

      if (conferences.length) {
        // console.log('conference participant fetch', conferences);
        const participants = await twilio.conferences(conferences[0].sid).participants.list();
        // a non started conference has zero participants
        if (!participants.length) {
          // console.log('conference complete', { status: 'completed' });
          await twilio.conferences(conferences[0].sid).update({ status: 'completed' });
        }
      }

      // Workaround related to calls being rejected, this code can be removed when twilio
      // solve the issue about sending complete events with CallStatus "failed" and "Busy"
      // Make sure the task is marked completed
      try {
        // console.log('markTaskAsCompleted 2', params);
        await markTaskAsCompleted(params, workspace);
      } catch (e) {
        // Skip log when task is already completed.
        if (e.status !== 400 && !e.message.includes('not currently assigned')) {
          console.error(params, e);
        }
      }

      if (params.connectType === 'phone') {
        try {
          // console.log('updateWorkerActivity 2', activity.coolDown, params.WorkerSid);
          await updateWorkerActivity(activity.coolDown, params.WorkerSid, workspace);
        } catch (e) {
          console.error(params, e);
        }
      }

      return callback(null, response(200, voiceResponse));
    }
  }

  // New outbound conference request for sip/phone & location client
  if (params.connectType === 'phone' || params.connectType === 'location') {
    // Check and confirm bypass press 1
    if ((!('bypass_press_1' in params) || parseInt(params.bypass_press_1, 10) !== 1) && !('Digits' in params)) {
      params.check_bypass = 1;
      const lambdacallApiHost = new URL(config.call_url).origin;
      const actionUrl = `${lambdacallApiHost}/${CONFIG_ENV}-outbound-callback/handler`;

      // Instructions for the IVR
      const gatherSayMessageRepetitions = 4;
      const gatherPauseBetweenMessages = 3;
      const gather = voiceResponse.gather({
        numDigits: 1,
        timeout: 10,
        method: 'GET',
        action: `${actionUrl}?${querystring.stringify(params, '&', '=')}`,
      });

      for (let i = 0; i < gatherSayMessageRepetitions; i++) {
        gather.say('Press 1 to connect with the customer.');
        gather.pause({ length: gatherPauseBetweenMessages });
      }

      // Agent takes to much time to anwser terminate the call
      params.Digits = '0'; // Set explicitly twilio Digits value different to 1 to terminate task
      voiceResponse.say('Disconnecting... response took too long.');

      // Always is sent user interacion to the outbound callback
      voiceResponse.redirect(`${actionUrl}?${querystring.stringify(params, '&', '=')}`);

      return callback(null, response(200, voiceResponse));

    }

    if (!('check_bypass' in params) || (('Digits' in params) && parseInt(params.Digits, 10) === 1)) {
      // Either the agent pressed 1 or bypass_press_1 setting is active on location comunication settings
      voiceResponse = await outboundConference(params, workspace, twilio, locClient);
      return callback(null, response(200, voiceResponse));
    }

    // Either agent not pressed a number or pressed a different than number 1
    await markTaskAsCompleted(params, workspace);
    if (params.connectType === 'phone') {
      await updateWorkerActivity(activity.coolDown, params.WorkerSid, workspace);
    }
    voiceResponse.hangup();
    return callback(null, response(200, voiceResponse));

  }

  // New outbound conference request for agent when connected by computer
  return callback(null, response(200, await outboundConference(params, workspace, twilio, locClient)));
}

async function outboundConference(params, workspace, twilio, locClient) {

  const locDetail = await getLocation(params, locClient);
  params.accountId = locDetail.user_id;
  params.locationId = locDetail.location_id;
  params.record_outgoing = locDetail.record_outgoing;

  await logOutboundCall(params, params.CallSid)
    .catch(e => console.error('outboundConference', e));

  let workerData = {};

  if (params.connectType === 'phone') {
    workerData = {
      activitySid: (await workspace.activities.list({ friendlyName: activity.onCall }))[0].sid,
    };
  }

  // console.log('OUTBOUND CONFERENCE', params);

  // To notify the agent when accepted to continue with the a followup or collection call.
  if (params.callType !== 'outbound') {
    workerData.attributes = await getWorkerAttributes(params, workspace)
      .catch(e => console.error('outboundConference', e));
  }

  if (workerData != {}) {
    await workspace.workers(params.WorkerSid).update(workerData)
      .catch(e => console.error('outboundConference', e));
  }

  let voiceResponse;
  try {
    voiceResponse = await dialConference(params, twilio, workspace, locDetail);
  } catch (e) {
    console.error("dialConference Error", e, workspace, params, twilio);
    voiceResponse = new Twilio.twiml.VoiceResponse();
    voiceResponse.say(grammarNazi(e.message));
    voiceResponse.hangup();
    await markTaskAsCompleted(params, workspace);
  }
  return voiceResponse;
}

function grammarNazi(msg){
  /*

   CPAPI-1621
   When an outbound call attempts to ring an invalid number,
   Twilio will give us an error message that looks like this:

   "Called is not valid: 1111111111"

   Aside from being bad grammar, the phone number is read back
   as "one million one hundred thousand..." etc.

   This function will look for that message and rewrite it as:

   "The phone number you are attempting to call, 1 1 1 1 1 1 1 1 1 1, is not valid."

   */
  const parts = [
    'The phone number you are attempting to call',
    'is not valid.'
  ];
  const sayItGood = (numSpaced) => `${parts[0]}, ${numSpaced}, ${parts[1]}`;
  if (msg && msg.startsWith("Called is not valid")){
    const numSpaced = msg.split(':')[1].trim().split('').join(' ');
    return sayItGood(numSpaced);
  }

  /*

   CCC-165
   New case:
   "The phone number you are attempting to call, 1111111111, is not valid."

   */
  if (msg && msg.startsWith("The phone number you are attempting to call")){
    const numSpaced = msg.split(',')[1].trim().split('').join(' ');
    return sayItGood(numSpaced);
  }

  return msg;
}

async function getWorkerAttributes(params, workspace) {
  const worker = await workspace.workers(params.WorkerSid).fetch();
  return JSON.stringify({
    ...JSON.parse(worker.attributes),
    conference: 'connected',
  });
}

async function dialConference(params, twilio, workspace, locDetail) {
  const lambdacallApiHost = new URL(config.call_url).origin;
  const baseUrl = `${lambdacallApiHost}/`;

  const queryStringParams = querystring.stringify({
    workspace_sid: params.workspace_sid,
    WorkerSid: params.WorkerSid,
    record_outgoing: params.record_outgoing,
    authToken: params.authToken,
    leadId: params.leadId,
    customerId: params.customerId,
    leadESId: params.leadESId,
    customerESId: params.customerESId,
    connectType: params.connectType,
    locationId: params.locationId,
    accountId: params.accountId,
    callSid: params.CallSid,
    report_id: params.reportId,
    toPhone: params.ToPhone,
  }, '&', '=');

  const recordingStatusParams = {
    connectType: "phone",
    locationId: params.locationId,
    AgentId: params.accountId,
    Src: "OutboundConference",
    Customer: params.CallSid,
    WorkspaceSid: params.workspace_sid,
    authToken: params.authToken,
    leadId: params.leadId,
    customerId: params.customerId,
    leadESId: params.leadESId,
    customerESId: params.customerESId,
    report_id: params.reportId,
    accountId: params.accountId,
  };
  const queryStringParamsRecordingStatus = querystring.stringify(recordingStatusParams, '&', '=');

  // console.log("DIAL CONFERENCE", params, recordingStatusParams);

  // Conference events and callback URL the first to enter in the conference stablish this setting
  // because of that is necesary to send on both, 'participant' and 'dial.conference'
  const statusUrl = `${baseUrl}${CONFIG_ENV}-conference-event/handler?${queryStringParams}`;
  const recordingStatusUrl = `${baseUrl}${CONFIG_ENV}-conference-event/handler?${queryStringParamsRecordingStatus}`;
  const waitUrl = 'https://twimlets.com/holdmusic?Bucket=com.twilio.music';

  const voiceResponse = new Twilio.twiml.VoiceResponse();


  const conferenceOptions = {
    statusCallback: statusUrl,
    statusCallbackEvent: 'start join leave end',
    record: (params.record_outgoing === 1),
    recordingStatusCallback: recordingStatusUrl,
    waitUrl: `${waitUrl}.ambient`,
    startConferenceOnEnter: true,
    endConferenceOnExit: true,
  };

  try {

    let agentCall = await twilio.calls(params.CallSid).fetch();

    // This happens when agent is quickly hanging up call and completes before we create the conference
    if (agentCall.status === 'completed') {
      console.error('Agent leg of call already ended before being added to conference', params.CallSid);
      throw Error('Agent leg of call already ended before being added to conference');
    }

    /**
     * Create a normal call to the customer and pass along the conference options to call URL.
     * Twilio will call the URL to get the TwiML to execute when the call is answered.  The
     * call URL will return TwiML to connect the call to the conference via a <Dial><Conference> verb.
     */
    const customerDialArgs = {
      announce: '',
      confName: params.Sid,
      confOpts: conferenceOptions,
    };
    if (params.record_outgoing === 1 && locDetail.record_call_announcement) {
      customerDialArgs.announce = locDetail.record_call_announcement;
    }
    let participantCall = await twilio.calls.create({
      to: params.ToPhone,
      from: params.FromPhone,
      url: `${config.call_url}twilio/outbound-call-connect?argsBase64=${Buffer.from(JSON.stringify(customerDialArgs)).toString('base64')}`
    });

    await workspace.tasks(params.Sid)
      .fetch()
      .then(async(task) => {
        let taskAttrb = JSON.parse(task.attributes);
        const attribsUpdate = {
          outboundCustomerCallSid: participantCall.sid,
          conference: {
            sid: params.Sid, //we only have the friendly name of the conference. conference is not yet created
            participants: {
              customer: participantCall.sid,
              worker: params.CallSid
            }
          }
        };
        await task.update({
          attributes: JSON.stringify({ ...taskAttrb, ...attribsUpdate })
        })
        .catch(error => console.log(error, new Error().stack, attribsUpdate));
    });

    if (params.connectType === 'phone' || params.connectType === 'location') {
      voiceResponse.say('Connecting you to the customer. Please hold.');
    }

    let customerCall = await twilio.calls(participantCall.sid).fetch();

    if (customerCall && customerCall.status === 'failed') {
      await workspace.tasks(params.Sid)
        .fetch()
        .then(async(task) => {
          let taskAttrb = JSON.parse(task.attributes);
          let updateData = {'callCancelReason': 'invalidNumber'};
          await task.update({
            attributes: JSON.stringify({ ...taskAttrb, ...updateData })
          })
          .catch(error => console.log(error, new Error().stack, updateData));
      });
      await markTaskAsCompleted(params, workspace);
      voiceResponse.say('The phone number you are attempting to call is not valid.');
      return voiceResponse.hangup();
    }
  } catch (e) {
    console.error(e, conferenceOptions, twilio.accountSid);
    throw e;
  }

   // configuration for the agent end of the conference.
  const agentConferenceOptions = {
    statusCallback: statusUrl,
    statusCallbackEvent: 'start join leave end',
    waitUrl: `https://twimlets.com/echo?Twiml=%3CResponse%3E%0A%3CPlay%20loop%3D%220%22%3Ehttps%3A%2F%2Fcallpotential-sys-cdn.s3.us-west-2.amazonaws.com%2Fcallcenter%2Fob_call_volume_normalized.mp3%3C%2FPlay%3E%0A%3C%2FResponse%3E&`
  }

  // Redirect call to join the created conference
  return voiceResponse.dial().conference(agentConferenceOptions, params.Sid);
}

async function logOutboundCall(params, callSid) {
  const callClient = new cpapiClient.callClient(params.authToken);

  const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
  await redisClient.connect();
  let dbLogId = await redisClient.get(`outboundTask_${params.Sid}`);
  await redisClient.disconnect();

  let fkLeadId = 0;
  if (params.leadId) {
    fkLeadId = parseInt(params.leadId);
  }
  let customerId = 0;
  if (params.customerId) {
    customerId = params.customerId;
  }

  let ledgerId = 0;
  if (params.ledgerId) {
    ledgerId = params.ledgerId;
  }

  const payload = {
    employee_id: params.agentId,
    datestamp: moment.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss'),
    call_type: params.callType,
    location_id: params.locationId,
    account_id: params.accountId,
    is_auto_call: 0,
    twilio_id: callSid,
    fk_lead_id: fkLeadId,
    call_duration: 0,
    duration: 0,
    recording_url: '',
    customer_id: customerId,
    ledger_id: ledgerId,
    workflow_step: params.workflowStep
  };

  if (!payload.fk_lead_id){
    delete payload.fk_lead_id;
  }

  if(payload.customer_id) {
    delete payload.fk_lead_id;
  }

  // console.log('LOG OUTBOUND CALL', payload);

  // log to mysql
  const updatedCallData = await callClient.putData(`calldetail/${dbLogId}`, payload)
    .then((detail) => {
      payload.db_log_id = dbLogId;
      payload.call_number = detail.call_number;
      payload.call_name = detail.call_name;
    }).catch(error => console.error('logOutboundCall-calldetail', error));

  if (updatedCallData && !updatedCallData.customer_id && !params.leadId) {
    const intClient = new cpapiClient.intClient(params.authToken);
    const tenant = await intClient
      .postData('disposition', { phone: params.ToPhone, location_id: params.locationId })
      .then(dispositions => dispositions.items[0])
      .catch(error => console.error('logOutboundCall-disposition', error));

    if (tenant) {
      payload.fk_lead_id = tenant.lead_id;
      payload.es_lead_id = tenant.es_lead_id;

      if (params.customerESId) {
        payload.es_tenant_id = params.customerESId;
      } else {
        payload.customer_id = tenant.tenant_id;
        payload.es_tenant_id = tenant.es_tenant_id;
      }
    }
  }

  // Update track_callcenter_tasks entries with log id
  try {
    let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${params.Sid}`);
    if (taskList && taskList.items && taskList.items.length > 0) {
      await taskList.items.forEachAsync(async (element) => {
        element.log_id = payload.db_log_id
        await callClient.putData(`trackcallcentertasks/${element.id}`, element);
      });
    }
  } catch (e) {
    console.error('Error in updateing track_callcenter_tasks record', e);
  }

  // log to dynamo db
  return callClient.postData('call', payload);
}

async function getLocation(params, locClient) {
  let locationId;
  if (params.leadLocationId) {
    locationId = params.leadLocationId;
  } else if (params.ledgerLocationId) {
    locationId = params.ledgerLocationId;
  } else if (params.fromLocationId) {
    locationId = params.fromLocationId;
  }

  /*
    CPAPI-1756
    Testing for Outbound Call Recording uncovered an issue where the following
    code was requesting location details from `/location/${locationId}` which
    doesn't return the `record_outgoing` flag.  Therefore, Outbound calls were
    NOT honoring the configuration and NOT being recorded.
    There is a new endpoint that needs to be used, `locationconfiguration`
    http://stage-loc.callpotential.com/swagger-ui/#!/Location_Configuration_Management/LocationConfigurationGetById
  */
  return (locationId)
    ? locClient.getLocationConfiguration(locationId)
    : Promise.resolve({});
}

async function twilioCredentials(coreClient) {
  try {
    const session = await coreClient.getData('session');
    const account = await dynamoQuery(session.user.sid);
    account.userId = session.user.user_id;
    account.email = session.user.email;
    return account;
  } catch (e) {
    console.error('ERROR obtaning twilio credentials', e);
    return {};
  }
}

async function dynamoQuery(accountSid) {
  const params = {
    TableName: config.dynamodb.acctTable,
    IndexName: 'account_sid-index',
    KeyConditionExpression: 'account_sid = :sid',
    ExpressionAttributeValues: { ':sid': accountSid },
  };

  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
  try {
    const data = await dynamo.query(params).promise();
    return data.Items[0] || {};
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

function response(statusCode = 200, message = new Twilio.twiml.VoiceResponse()) {
  return {
    statusCode,
    headers: { 'Content-Type': 'text/xml' },
    body: message.toString(),
  };
}

async function markTaskAsCompleted(params, workspace) {
  // console.log('markTaskAsCompleted in', { assignmentStatus: 'completed' });

  try {
    await workspace.tasks(params.Sid).update({ assignmentStatus: 'completed' });
  } catch (e) {
    // Skip log when task is already completed.
    if (e.status !== 400 && !e.message.includes('not currently assigned')) {
      console.error(params, e);
    }
  }
}

async function updateWorkerActivity(activityName, workerSid, workspace) {
  const activitySid = (await workspace.activities.list({ friendlyName: activityName }))[0].sid;
  // console.log('updateWorkerActivity in', { activitySid });
  return workspace.workers(workerSid).update({ activitySid });
}

function noOp(event, context) {
  // console.log(event);
  context.done();
}

module.exports = {
  handler,
  no_op: noOp,
};
