import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { CallPotentialHttpApiService } from './callpotential-http-api.service';
import { Tenant, TenantDataErrorResponse, TenantDataRequest, TenantDataResponse, TenantDataSuccessfulResponse, TenantId } from '../models/tenant.model';
import { Ledger, LedgerDataRequest, LedgerDataResponse } from '../models/ledger.model';
import { PaymentHashRequest, AmountDueRequest, SavedCardsResponse, AmountDueResponse, PaymentHashResponse, PaymentHashDetailsResponse, PaymentData, TenantPaymentResponse, AmountDueErrorResponse, AmountDueSuccessfulResponse, TenantPaymentErrorResponse, WithResponseMetadata } from '../models/payment.model';
import { DomainEventsService } from './domain-events.service';
import { AxiosError } from 'axios';
import { BugsnagService } from './bugsnag.service';

@Injectable()
export class IntegrationService extends CallPotentialHttpApiService {
    constructor(
        protected override httpService: HttpService,
        protected override configService: ConfigService,
        protected override domainEventService: DomainEventsService,
        private readonly bugsnagService: BugsnagService,
    ) {
        super(httpService, configService, domainEventService, 'API_INT_URL');
    }

    public async getTenantData(request: TenantDataRequest): Promise<TenantDataResponse> {
        const isAnyFilterDefined = Boolean(request.unit_name || request.filterType || request.filterPhone ||
            request.filterEmail || request.filterFirstName || request.filterLastName ||
            request.filterName || request.filterExcludeAlternate || request.search);

        const endpoint = '/tenant';
        const params = {
            ...request,
            filterActive: isAnyFilterDefined,
            page: request.page || 1,
        };

        this.bugsnagService.leaveBreadcrumb('IntegrationService.getTenantData', { params });

        try {
            return await this.get(endpoint, params) as TenantDataSuccessfulResponse;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            return {} as TenantDataErrorResponse;
        }
    }

    public async getTenantById(tenantId: TenantId): Promise<Tenant | null> {
        const endpoint = `/tenant/${tenantId}`;
        this.bugsnagService.leaveBreadcrumb('IntegrationService.getTenantById', { tenantId });

        try {
            return await this.get(endpoint) as Tenant;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            return null;
        }
    }

    public async getLedgerData(tenantId: TenantId, request: LedgerDataRequest = {}): Promise<LedgerDataResponse> {
        const endpoint = `/tenant/${tenantId}/ledger`;
        this.bugsnagService.leaveBreadcrumb('IntegrationService.getLedgerData', { tenantId, request });

        try {
            return await this.get(endpoint, request) as LedgerDataResponse;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            return {} as LedgerDataResponse;
        }
    }

    public async getSavedCards(tenantId: TenantId, filterByLedgers: string[] = []): Promise<SavedCardsResponse> {
        const endpoint = `/tenant/${tenantId}/saved_cards`;
        const params = filterByLedgers.length > 0 ? { filterByLedgers } : undefined;
        this.bugsnagService.leaveBreadcrumb('IntegrationService.getSavedCards', { tenantId, filterByLedgers });

        try {
            return await this.get(endpoint, params) as SavedCardsResponse;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            throw error;
        }
    }

    public async getAmountDue(tenantId: TenantId, request: AmountDueRequest[]): Promise<AmountDueResponse> {
        const endpoint = `/tenant/${tenantId}/amount_due`;
        this.bugsnagService.leaveBreadcrumb('IntegrationService.getAmountDue', { tenantId, request });

        try {
            const body = JSON.stringify(request);
            return (await this.post(endpoint, body)).data;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            return { error: true } as AmountDueErrorResponse;
        }
    }

    public async createPaymentHash(request: PaymentHashRequest): Promise<PaymentHashResponse> {
        const endpoint = '/payment_hash';
        const body = {
            ...request,
            source: "call",
        };
        this.bugsnagService.leaveBreadcrumb('IntegrationService.createPaymentHash', { request });

        try {
            return (await this.post(endpoint, body)).data as PaymentHashResponse;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            throw error;
        }
    }

    public async getPaymentHashDetails(paymentHash: string): Promise<PaymentHashDetailsResponse> {
        const endpoint = `/payment_hash/${paymentHash}`;
        this.bugsnagService.leaveBreadcrumb('IntegrationService.getPaymentHashDetails', { paymentHash });

        try {
            return await this.get(endpoint) as PaymentHashDetailsResponse;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            throw error;
        }
    }

    public async makeTenantPayment(tenantId: TenantId, paymentData: PaymentData): Promise<WithResponseMetadata<TenantPaymentResponse>> {
        this.bugsnagService.leaveBreadcrumb('IntegrationService.makeTenantPayment', {
            tenantId,
            amount: paymentData.amount, // Example: add non-sensitive fields
        });

        if (paymentData.card_number === "3005739193013242") {
            /**
            * FOR TESTING PURPOSES
            */
            return {
                data: {
                    success: false,
                    error_msg: "Payment failed due to use of Invalid Test Card (makeTenantPayment)"
                } as TenantPaymentErrorResponse,
                status: 500
            };
        }

        try {
            const endpoint = `/tenant/${tenantId}/payment`;
            const body = JSON.stringify(paymentData);
            return await this.post(endpoint, body);
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            if (error instanceof AxiosError) {
                return {
                    data: {
                        success: false,
                        error_msg: error.response?.data?.error_msg || error.message
                    } as TenantPaymentErrorResponse,
                    status: error.response?.status || 500
                };
            }
            return {
                data: {
                    success: false,
                    error_msg: 'An unexpected error occurred'
                } as TenantPaymentErrorResponse,
                status: 500
            };
        }
    }

    public async makeTenantBulkPayment(tenantId: TenantId, paymentDataArray: PaymentData[]): Promise<WithResponseMetadata<TenantPaymentResponse>> {
        this.bugsnagService.leaveBreadcrumb('IntegrationService.makeTenantBulkPayment', {
            tenantId,
            paymentCount: paymentDataArray.length,
        });

        let testingWithInvalidTestCard = false;
        for (const paymentData of paymentDataArray) {
            if (paymentData.card_number === "3005739193013242") {
                testingWithInvalidTestCard = true;
            }
        }
        if (testingWithInvalidTestCard) {
            /**
            * FOR TESTING PURPOSES
            */
            return {
                data: {
                    success: false,
                    error_msg: "Payment failed due to use of Invalid Test Card (makeTenantBulkPayment)"
                } as TenantPaymentErrorResponse,
                status: 500
            };
        }

        try {
            const endpoint = `/tenant/${tenantId}/payment_bulk`;
            const body = JSON.stringify(paymentDataArray);
            return await this.post(endpoint, body);
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            if (error instanceof AxiosError) {
                return {
                    data: {
                        success: false,
                        error_msg: error.response?.data?.error_msg || error.message
                    } as TenantPaymentErrorResponse,
                    status: error.response?.status || 500
                };
            }
            return {
                data: {
                    success: false,
                    error_msg: 'An unexpected error occurred'
                } as TenantPaymentErrorResponse,
                status: 500
            };
        }
    }

    public async calculateAmountDue(units: Ledger[], prepayMonths = 0): Promise<number> {
        let totalAmountDue = 0;

        try {
            for (const unit of units) {
                this.bugsnagService.leaveBreadcrumb('IntegrationService.calculateAmountDue', {
                    tenantId: unit.tenant_id_es,
                    ledgerId: unit.ledger_id,
                });

                const amountDueRequest = {
                    ledger_id: unit.ledger_id,
                    unit_id: unit.es_unit_id,
                    prepay_month: prepayMonths,
                };
                const amountDueResponse = await this.getAmountDue(unit.tenant_id_es, [amountDueRequest]);

                if ((amountDueResponse as AmountDueErrorResponse).error) {
                    throw new Error(`Error retrieving the amount due for tenant ${unit.tenant_id_es} ${JSON.stringify(amountDueRequest)}`);
                }
                const successfulResponse = amountDueResponse as AmountDueSuccessfulResponse[];
                const amountDue = Number(successfulResponse[0].meta.total);
                unit.amount_owed = amountDue.toFixed(2);
                totalAmountDue += amountDue;
            }
            return totalAmountDue;
        } catch (error) {
            this.bugsnagService.notify(error as Error);
            throw error;
        }
    }
}