'use strict';
const AWS = require('aws-sdk');
var twilio = require('twilio');
var moment = require('moment-timezone');
var _ = require('lodash');
var config = require('../../config/config');
const console_c = require('../../config/logger').console;
var country_dialing_code = require('../../config/country-codes');
const redis = require('redis');
const task_list_map = config.twilio.task_list_map;
const {getTwilioClient} = require('../../libraries/common-methods')
var LocationModel = require('../../models/location').LocationModel;
const bugsnagService = require('../../libraries/bugsnag-service');
const CallLogModel = require('../../models/call-log');

// eslint-disable-next-line no-useless-escape
const SIP_PATTERN = /[^\:]*\:([0-9]*)@.*/;

Array.prototype.forEachAsync = async function (fn) {
  for (let t of this) { await fn(t) }
}

async function getCallInfo(callSid) {
  let callInfo = await new CallLogModel().list_dynamodb(callSid, 'twilio_id');
  if (!callInfo || Object.keys(callInfo).length === 0) {
    callInfo = await new CallLogModel().list_dynamodb(callSid, 'log_id');
  }
  return callInfo;
}

async function getTaskSid(logId) {
  try {
    const cpapiClient = require('../../libraries/cpapi-client');
    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
    let taskList = await callClient.getData(`trackcallcentertasks?filterLog_id=${logId}`);
    if (taskList && taskList.items && taskList.items.length > 0) {
      const mainTask = taskList.items.find(task => task.is_task === 1);
      if (mainTask) {
        return mainTask.task_sid;
      }
    }
  } catch (e) {
    console.debug(`Error finding task SID: ${e.message}`);
  }
  return null;
}

async function findConference(twilioClient, taskSid, callSid) {
  let conferences = await twilioClient.conferences.list({
    friendlyName: taskSid,
    status: 'in-progress',
  });
  if (conferences.length > 0) {
    return conferences[0];
  }

  const allConferences = await twilioClient.conferences.list({
    status: 'in-progress',
  });
  for (const conf of allConferences) {
    try {
      const participants = await twilioClient.conferences(conf.sid).participants.list();
      if (participants.some(p => p.callSid === callSid)) {
        console.debug(`Found conference ${conf.sid} with customer call ${callSid}`);
        return conf;
      }
    } catch (err) {
      console.debug(`Error checking conference ${conf.sid}: ${err.message}`);
    }
  }
  return null;
}

async function handleConferenceTermination(twilioClient, conference, taskSid, accDetail) {
  const participants = await twilioClient.conferences(conference.sid).participants.list();

  if (participants.length === 1) {
    const participant = participants[0];
    const isAgent = participant.label && participant.label.includes('agent');
    if (isAgent) {
      try {
        await twilioClient.calls(participant.callSid).update({ status: 'completed' });
      } catch (err) {
        console.debug(`Error terminating agent call: ${err.message}`);
      }
      try {
        const workspace = twilioClient.taskrouter.workspaces(accDetail.workspace_sid);
        await workspace.tasks(taskSid).update({
          assignmentStatus: 'completed',
          reason: 'hangup'
        });
      } catch (err) {
        console.debug(`Error updating task: ${err.message}`);
      }
    }
  }
}

/**
 * Called from twilio hook whenever call's status changes also used for recording callback
 */
var twilio_status_callback = async function(get_data, post_data) {
  console_c.log("TWILIO STATUS CALLBACK");
  console_c.log('GET_DATA', get_data);
  console_c.log('POST_DATA', post_data);

  let twimle = new twilio.twiml.VoiceResponse();

  if (!post_data) {
    return twimle;
  }

  // try {
  //   if (post_data['CallStatus'] && ['completed', 'failed', 'busy', 'no-answer'].includes(post_data['CallStatus'])) {
  //     console.debug(`Call ${post_data['CallSid']} ended with status: ${post_data['CallStatus']}`);
  //     try {
  //       const callInfo = await getCallInfo(post_data['CallSid']);
  //       if (!callInfo || Object.keys(callInfo).length === 0) {
  //         console.debug(`No call info found for CallSid: ${post_data['CallSid']}`);
  //         return twimle;
  //       }

  //       const logId = callInfo.log_id;
  //       if (!logId) {
  //         console.debug(`No logId found in callInfo`);
  //         return twimle;
  //       }

  //       const accDetail = await new CallLogModel().get_account_dynamo(callInfo.account_id);
  //       if (!accDetail) {
  //         console.debug(`No account details found for account_id: ${callInfo.account_id}`);
  //         return twimle;
  //       }

  //       const twilioClient = twilio(accDetail.account_sid, accDetail.authtoken);
  //       const taskSid = await getTaskSid(logId);
  //       if (!taskSid) {
  //         console.debug(`No task SID found for log_id: ${logId}`);
  //         return twimle;
  //       }

  //       const conference = await findConference(twilioClient, taskSid, post_data['CallSid']);
  //       if (!conference) {
  //         console.debug(`No conference found for task ${taskSid}`);
  //         return twimle;
  //       }

  //       await handleConferenceTermination(twilioClient, conference, taskSid, accDetail);
  //     } catch (e) {
  //       console.debug(`Error handling conference termination: ${e.message}`);
  //     }
  //   }
  // } catch (e) {
  //   console.debug(`Error in twilio_status_callback: ${e.message}`);
  // }

  try {
    if (post_data['RecordingUrl'] && post_data['RecordingStatus'] === 'completed') {
      console.debug("Received recording, waiting 3 seconds.");
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.debug("Done waiting for recording.");
      let call_info = await new CallLogModel().list_dynamodb(get_data['log_id'], 'log_id');

      let location_id = Number(call_info['location_id']);
      let log_id = Number(call_info['log_id'])

      var RecordingUrl = post_data['RecordingUrl'];
      var RecordingSid = post_data['RecordingSid'];
      var RecordingDuration = (post_data['RecordingDuration'] > 0) ? post_data['RecordingDuration'] : 0;

      // Add Bugsnag breadcrumb for successful recording
      bugsnagService.leaveBreadcrumb('Recording URL received', {
        callSid: post_data['CallSid'],
        logId: log_id,
        locationId: location_id,
        recordingUrl: RecordingUrl,
        recordingSid: RecordingSid,
        recordingDuration: RecordingDuration,
        recordingStatus: post_data['RecordingStatus']
      });

      await new CallLogModel().update_dynamodb({
        'twilio_id': call_info['twilio_id'],
        'location_id': location_id,
        'log_id': log_id
      }, {
        'recording_url': RecordingUrl,
        'recording_sid': RecordingSid,
        'call_duration': RecordingDuration,
        'is_route_complete': 1
      })
      return twimle;
    } else {
      try {
        let call_info = await new CallLogModel().list_dynamodb(post_data['CallSid'], 'twilio_id');
        if (call_info && Object.keys(call_info).length !== 0) {
          let location_id = Number(call_info['location_id']);
          let log_id = Number(call_info['log_id'])

          await new CallLogModel().update_dynamodb({
            'twilio_id': call_info['twilio_id'],
            'location_id': location_id,
            'log_id': log_id
          }, {
            'is_route_complete': 1
          })
        }

        return twimle;
      } catch (e) {
        console.error(e, new Error().stack);
        // Add Bugsnag error tracking
        bugsnagService.notify(e, {
          metadata: {
            call: {
              callSid: post_data['CallSid'],
              error: 'Failed to update call info without recording',
              stack: e.stack
            }
          }
        });
        return twimle;
      }
    }
  } catch (e) {
    console.error(e, new Error().stack);
    // Add Bugsnag error tracking
    bugsnagService.notify(e, {
      metadata: {
        call: {
          callSid: post_data['CallSid'],
          error: 'Recording URL processing failed',
          stack: e.stack
        }
      }
    });
    return twimle;
  }
}

var update_twillio_call = async function(data) {
  const client = twilio(data.sid, data.authtoken);
  let request_url = config.call_url + 'twilio/leave_queue_redirect/' +
    data.TaskAttributes.log_id + '/' + data.TaskAttributes.queue_id;
  let updateData = {
    url: request_url,
    method: "POST"
  }
  await client.calls(data.TaskAttributes.call_sid)
    .update(updateData)
    .catch(async (err) => {
      console.error("ERROR in call redirecting", err);

      // wait 2 seconds before trying again
      await new Promise(resolve => setTimeout(resolve, 2000));
      await client.calls(data.TaskAttributes.call_sid)
        .update(updateData)
        .catch(async (err) => {
          console.error("ERROR in 2nd call redirecting", err);
        })
  });
}

var update_callcener_task = async function(data) {
  let update_task_col = 'is_abandoned';
  let update_col_val = 1;

  if ('task.canceled' === data.EventType) {
    if (data.Reason === 'Task TTL Exceeded') {
      // In v2 we are not setting timeout in workflow filters so workflow.timeout will not be there
      // Instead, we are checking task cancel reason as TTL expiring is similar to workflow.timeout
      // And based on that setting is_rolled_over flag
      update_task_col = 'is_rolled_over';
    } else if (data.Reason === 'leave') {
      // Made a request to skip step
      update_col_val = 9;
    } else if (data.Reason === 'Scheduled Callback') {
      // Made a request to callback
      update_col_val = 8;
    }
  }

  try {
    const cpapiClient = require('../../libraries/cpapi-client');
    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);
    let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${data.TaskSid}`);
    if (taskList && taskList.items && taskList.items.length > 0) {
      await taskList.items.forEachAsync(async (element) => {
        if (element.is_task == 1) {
          element[update_task_col] = update_col_val;
          element['queue_time'] = data.TaskAge;
          await callClient.putData(`trackcallcentertasks/${element.id}`, element);
        }
      });
    }

    return true;
  } catch (e) {
    console.error('Error in updateing track_callcenter_tasks record', e, new Error().stack);

    return false;
  }
}

var make_leave_queue_request = async function(data) {
  var CallLogModel = require('../../models/call-log');
  if (data.TaskAttributes) {
    data.TaskAttributes = data.TaskAttributes.trim() ? JSON.parse(
      data.TaskAttributes
    ) : data.TaskAttributes.trim();
  }

  var call_log = null;

  let log_detail = await new CallLogModel().list_dynamodb(data.TaskAttributes.log_id);
  if (!log_detail) {
    return;
  } else {
    call_log = log_detail;
    let acc_detail = await new CallLogModel().get_account_dynamo(call_log.account_id);
    if (!acc_detail) {
      console_c.log("# no dynamodb entry found for account #", call_log.account_id);
      call_log = null;
    } else {
      call_log.sid = acc_detail.account_sid;
      call_log.authtoken = acc_detail.authtoken;
      data.sid = acc_detail.account_sid;
      data.authtoken = acc_detail.authtoken;
    }
  }

  console_c.log("CPBUG-351: make_leave_queue_request current_route_step", call_log.current_route_step);

  call_log.current_route_step = call_log.current_route_step ? JSON.parse(
    call_log.current_route_step
  ) : call_log.current_route_step.trim();

  // Save data related to call is_rolled_over and is_abandoned events
  // into the task create row of call and time in queue for call task.
  await update_callcener_task(data);

  if ('task.canceled' === data.EventType && data.Reason === 'Scheduled Callback') {
    return;
  }

  // Make the call leave the queue only in case of queue timeout event
  // and not in the case of task canceled since customer
  // call has already ended.
  // CP-8878 Leave queue on press 9 by user
  // in this case no need to redirect call to leave_queue_redirect as queue already left and call moved to next step
  if ('task.canceled' === data.EventType && data.TaskAge < data.TaskAttributes.timeout && data.Reason === 'leave') {
    return;
  }

  // Save the next step to be processed.
  let next_step = await new CallLogModel().queue_save_next_step(call_log);

  console_c.log("CPBUG-351: make_leave_queue_request next_step", JSON.stringify(next_step));

  // Update call to leave queue in case of task queue timeout.
  let update_twi_res = await update_twillio_call(data);
  return update_twi_res;
}

var make_location_leave_queue_request = async function(log_id) {
  var CallLogModel = require('../../models/call-log');

  console_c.log("MAKE LOCATION LEAVE");
  let call_log = null;

  // Get current call route step and twilio related info.
  var CallLogQuery = new CallLogModel();
  call_log = await CallLogQuery.list_dynamodb(log_id)
    .catch(function(err) {
      console_c.log("# leave queue error #", err);
    });

  if (!call_log) {
    return;
  }

  console_c.log("CPBUG-351: make_location_leave_queue_request current_route_step", call_log.current_route_step);

  call_log.current_route_step = call_log.current_route_step ? JSON.parse(
    call_log.current_route_step
  ) : call_log.current_route_step.trim();

  // Save the next step to be processed.
  let next_step = await new CallLogModel().queue_save_next_step(call_log);    
  call_log.current_route_step = JSON.stringify(next_step);

  console_c.log("CPBUG-351: make_location_leave_queue_request next_step", call_log.current_route_step);

  return call_log;
}

var worker_activity_update_request = async function(data) {
  if (!data.WorkerAttributes)
    data.WorkerAttributes = '';
  data.worker_attributes = data.WorkerAttributes.trim() ? JSON.parse(
    data.WorkerAttributes
  ) : data.WorkerAttributes.trim();

  const cpapiClient = require('../../libraries/cpapi-client');
  const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);

  let statusLogPayload = {
    'user_id': data.worker_attributes.agent_id,
    'activity_sid': data.WorkerActivitySid,
    'start_time': moment.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss')
  };

  return await callClient.postData('workerstatuslogs', statusLogPayload);
}

var insert_task_activity = async function(data) {
  data.task_attributes = {};
  data.worker_attributes = {};

  if (data.TaskAttributes && typeof data.TaskAttributes === "string") {
    data.task_attributes = data.TaskAttributes.trim() ? JSON.parse(
      data.TaskAttributes
    ) : data.TaskAttributes.trim();
  }

  if (data.WorkerAttributes && typeof data.TaskAttributes === "string") {
    data.worker_attributes = data.WorkerAttributes.trim() ? JSON.parse(
      data.WorkerAttributes
    ) : data.WorkerAttributes.trim();
  }

  // Set the basic default task activity info.
  var activity = {
    'task_sid': data.TaskSid,
    'date_created': moment.unix(data.Timestamp).tz(config.TZ).format('YYYY-MM-DD HH:mm:ss'),
    'log_id': data.task_attributes.log_id,
    'dynamo_log_id': data.task_attributes.log_id,
    'config_step_uid': data.task_attributes.config_step_uid,
    'channel': data.TaskChannelUniqueName,
    'workflow': data.WorkflowName,
  };

  activity['queue_sid'] = data.task_attributes.queue_sid;
  
  switch (data.EventType) {
    case 'reservation.timeout':
    case 'reservation.rejected':
    case 'reservation.accepted':
    case 'reservation.created':
      // Set the action field by replacing "." with "_" to match
      // db column name.
      var action = data.EventType.replace('.', '_');

      // Update task activity related extra info for reservation action.
      activity[action] = data.ReservationSid;
      activity['agent_id'] = data.worker_attributes.agent_id;
      break;

    default:
      // While creating main task row set flag column is_task to 1.
      activity['is_task'] = 1;
      break;
  }

  try {
    const cpapiClient = require('../../libraries/cpapi-client');
    const callClient = new cpapiClient.callClient(config.db.serviceTokens.readWrite);

    if ('reservation.accepted' === data.EventType) {
      // Update call log with agent id
      // console.debug("BEFORE PUT REQUEST", JSON.stringify(data, null, 2));

      if (data.TaskChannelUniqueName === "voice") {
        await callClient.putData(`call/${data.task_attributes.location_id}/${data.task_attributes.call_sid}`, {
          'employee_id': data.worker_attributes.agent_id
        });
      } else if(data.TaskChannelUniqueName === "custom1") {
        await callClient.putData(`calldetail/${data.task_attributes.dbLogId}`, {
          'employee_id': data.worker_attributes.agent_id
        });
      }

      if (activity['is_task'] == 1) {
        activity['queue_time'] = data.TaskAge;
      } else {
        let taskList = await callClient.getData(`trackcallcentertasks?filterTask_sid=${data.TaskSid}`);
        if (taskList && taskList.items && taskList.items.length > 0) {
          taskList.items.forEach(async (element) => {
            if (element.is_task == 1) {
              element['queue_time'] = data.TaskAge;
              await callClient.putData(`trackcallcentertasks/${element.id}`, element);
            }
          });
        }
      }
    }

    await callClient.postData(`trackcallcentertasks`, activity);

    return true;
  } catch (e) {
    console.error('Error in updateing track_callcenter_tasks record', e, new Error().stack);
  
    return false;
  }
}

var get_dialing_code = async function(location_id, with_dc_prefix, get_country_code) {
  if (!with_dc_prefix)
    with_dc_prefix = false;
  if (!get_country_code)
    get_country_code = false;

  let res_return = '';
  let location = await new LocationModel().get_location_by_id(location_id);

  if (location.country) {
    res_return = location.country;
    if (!get_country_code) {
      if (country_dialing_code[res_return]) {
        var dial_code = country_dialing_code[res_return];
        res_return = (with_dc_prefix) ? ('+' + dial_code) : dial_code;
      }
    }
  }

  return res_return;
}

var remove_dialing_code = function(phone_number, dl_code) {
  if (typeof phone_number !== 'string') {
    return phone_number;
  }
  if (dl_code)
    dl_code = dl_code.toString();
  phone_number = phone_number.replace(/[^\d]+/, '');
  if (phone_number.indexOf(dl_code) === 0) {
    phone_number = phone_number.substring(dl_code.length);
  }
  return phone_number;
}

var add_dialing_code = function(phone_number, dl_code, add_prefix) {
  if (!add_prefix)
    add_prefix = true;
  phone_number = remove_dialing_code(phone_number, dl_code);
  if (phone_number.indexOf(dl_code) > -1)
    phone_number = dl_code + phone_number;
  phone_number = (add_prefix) ? ('+' + phone_number) : phone_number;
  return phone_number;
}

var generateRowId = function(subid) {
  var EPOCH = 1300000000000;
  var ts = new Date().getTime() - EPOCH; // limit to recent
  var randid = Math.floor(Math.random() * 512);
  ts = (ts * 64); // bit-shift << 6
  ts = (ts + subid);
  return (ts * 512) + (randid % 512);
}

var parseCallRouteVariables = async function(route_configs, location_id, user_id, destination_data) {
  if (route_configs.config && route_configs.config.indexOf('{custom.') !== -1) {
    var CommonMethod = require('../../libraries/common-methods').CommonMethod;
    var common_func = require('../../models/utils/common_func');

    var cObj = new CommonMethod();
    let template_values = await cObj.get_custom_variables_mapping({
      'parent_id': user_id,
      'location_id': location_id,
      destination_data
    });
    route_configs.config = common_func.findReplaceBulk(route_configs.config, template_values);
  }
}

var process_inbound_call_destination_data = async function(destination_data, data, call_number) {
  let t_client_resp = new twilio.twiml.VoiceResponse();
  let CallLogModel = require('../../models/call-log');
  let location_id = destination_data['location_id'],
    destination = destination_data['phone'],
    ad_id = destination_data['ad_id'],
    number_type = destination_data['type'],
    phone = data.From,
    check_dial_code = true; // Flag variable to indicate whether to check for dialing code.

  if (_.includes(phone, 'sip')) {
    var from_match = SIP_PATTERN.exec(phone);

    if (from_match) {
      phone = from_match[1];
    } else {
      // If SIP address does not contain a number and $from_match is empty, then set phone
      // number to empty string and do not check for dialing code.
      // CC-824.
      phone = '';
      check_dial_code = false;
    }
  }

  let dialing_code =  await get_dialing_code(location_id);
  if (check_dial_code) {
    phone = remove_dialing_code(phone, dialing_code);
    phone = add_dialing_code(phone, dialing_code);
  }

  let route_configs = destination_data['callRouteConfig'];

  if (!route_configs) {
    // Error if no call route config defined for the location owner
    return "<Response><Say>No call route defined for the location owner.</Say></Response>";
  }

  await parseCallRouteVariables(route_configs, location_id, destination_data.user_id, destination_data);

  let greeting_mp3 = '';
  let greeting = '';

  // Get audio file for the greeting if exist
  if (destination_data['greeting_type'] == 1 && destination_data['greeting_mp3']) {
    greeting_mp3 = config.CP_CDN_URL + 'uploads/location_greeting_mp3/' + destination_data['greeting_mp3'];
  } else if (destination_data['greeting']) {
    greeting = destination_data['greeting'];
  } else {
    greeting = "This call may be recorded.";
  }

  let inbound_caller_name = data.CallerName;

  // Check if CallerName is present in the request
  if (inbound_caller_name) {
    // Remove spaces from start and end of name
    // Replace multiple spaces with a single space
    inbound_caller_name = inbound_caller_name.trim().replace(/!\s+!/, ' ');

    // If ' ' character is found inside the caller name
    // Then extract the lastname and firstname and store them in the correct order
    if (inbound_caller_name.indexOf(' ') > -1) {
      let i = inbound_caller_name.indexOf(' ');
      let name_parts = [inbound_caller_name.slice(0, i), inbound_caller_name.slice(i + 1)];
      inbound_caller_name = name_parts[1].trim() + ' ' + name_parts[0].trim();
    }
  }

  // Save the calls info in the log
  let log_info = {
    'twilio_id': data.CallSid,
    'call_number': phone,
    'call_name': call_number,
    'datestamp': moment.tz(config.TZ).format('YYYY-MM-DD HH:mm:ss'),
    'call_type': (number_type == 'collection') ? 'inbound_collection' : data.Direction,
    'call_destination': destination,
    'location_id': location_id,
    'ad_id': ad_id,
    'caller_name': inbound_caller_name,
    'current_route_step': route_configs.config,
    'route_config': route_configs.config_id,
    'account_id': destination_data.user_id
  };

  if (route_configs.config.length === 0) {
    console_c.log("CPBUG-351: current_route_step is empty", log_info, route_configs, destination_data, data);
  }

  let toNumber = remove_dialing_code(phone, dialing_code);
  let dispositionPayload = {
    "phone": toNumber,
    "location_id": location_id,
    "run_reservation_cron": 0
  }

  if (destination_data['reservation_cron_on_unknown_call']) {
    dispositionPayload.run_reservation_cron = 1;
  }
  const cpapiClient = require('../../libraries/cpapi-client');
  const intClient = new cpapiClient.intClient(config.db.serviceTokens.readWrite);

  const responseBody = await intClient.postData('disposition', dispositionPayload);

  if (responseBody && responseBody['items']) {
    let tenants = responseBody['items'];

    if (tenants.length == 0) {
      console_c.log("disposition not found");
    } else {
      let record =  tenants[0];
      log_info.fk_lead_id = record.lead_id;
      log_info.es_lead_id = record.es_lead_id;
      log_info.customer_id = record.tenant_id;
      log_info.es_tenant_id = record.es_tenant_id;
      log_info.customer_type = record.type;
      log_info.customer_name = `${record.first_name} ${record.last_name}`;
      console_c.log("tenantId", record.tenant_id);
      console_c.log("leadId", record.lead_id);

      /**CPAPI-3832 Set disposition data in redis, to be used while Call syncing*/
      const redisClient = redis.createClient({'url': `redis://${config.redis.host}`});
      await redisClient.connect();
      let redisKey = `disposition_data_${toNumber}_${location_id}`;
      await redisClient.set(redisKey, JSON.stringify(record), {EX: 1800});

      await redisClient.disconnect();
    }
  }

  let log_id = generateRowId(5);
  log_info.log_id = log_id;
  console_c.log("log_info",log_info);
  
  // Add Bugsnag breadcrumb for inbound call creation
  bugsnagService.leaveBreadcrumb('Inbound call created', {
    callSid: data.CallSid,
    logId: log_id,
    locationId: location_id,
    callType: log_info.call_type,
    callNumber: phone,
    callDestination: destination,
    hasRouteConfig: !!route_configs,
    routeConfigLength: route_configs ? route_configs.config.length : 0
  });

  const callLogModel = new CallLogModel();
  let res_list = await callLogModel.put_dynamodb(log_info);
  if (!res_list || Object.keys(res_list).length === 0) {
    // Add Bugsnag error tracking for failed call creation
    bugsnagService.notify('Failed to create call log in DynamoDB', {
      severity: 'error',
      metadata: {
        call: {
          callSid: data.CallSid,
          logId: log_id,
          locationId: location_id,
          logInfo: log_info
        }
      }
    });
    return "<Response><Say>Something wrong with processing call.</Say></Response>";
  }

  let response_call_log = await callLogModel.create_call_log(log_info);
  if (!response_call_log) {
    // Add Bugsnag error tracking for failed call detail creation
    bugsnagService.notify('Failed to create call detail log', {
      severity: 'error',
      metadata: {
        call: {
          callSid: data.CallSid,
          logId: log_id,
          locationId: location_id,
          logInfo: log_info
        }
      }
    });
    return "<Response><Say>Something wrong with processing calldetail.</Say></Response>";
  }

  if (greeting_mp3) {
    t_client_resp.play(greeting_mp3);
  } else {
    t_client_resp.say(greeting);
  }

  t_client_resp.redirect(`${config.call_url}twilio/process_next_step/${log_id}?leave_queue=1`);

  let xml_res = decodeURIComponent(t_client_resp.toString());
  return xml_res;
}

var process_inbound_call = async function(data) {
  let call_number = data.To;

  if (_.includes(call_number, 'sip')) {
    var to_match = SIP_PATTERN.exec(call_number);
    call_number = '+' + to_match[1];
  }

  let accountData = await getAccountFromSid(data.AccountSid);

  // get location for which call intend to using tracking number
  let destination_data = await new LocationModel()
    .get_inbound_route_info(call_number.replace('+', ''), accountData.id, data.callRouteId);
  if (!destination_data) {
    return "<Response><Say>No call destination found.</Say></Response>";
  }
  return await process_inbound_call_destination_data(destination_data, data, call_number);
}

async function getAccountFromSid(accountSid) {
  const params = {
    TableName: config.dynamodb.acctTable,
    IndexName: 'account_sid-index',
    KeyConditionExpression: 'account_sid = :sid',
    ExpressionAttributeValues: { ':sid': accountSid },
  };

  const dynamo = new AWS.DynamoDB.DocumentClient({endpoint: config.dynamodb.endpoint});
  try {
    const data = await dynamo.query(params).promise();
    if (data.Items.length === 0) return {};

    return data.Items[0];
  } catch (e) {
    console.error('Error querying dynamo:', e);
    return {};
  }
}

var sync_task_map = async function(twilioAccount, data) {
  try {
    if (JSON.stringify(twilioAccount) === '{}') {
      return false;
    }
    
    let clientSync = await getTwilioClient(twilioAccount.account_sid,twilioAccount.authtoken)

    var syncService = clientSync.sync.v1.services(twilioAccount.sync_service_sid)
    
    const mapData = {
      EventDescription: data.EventDescription,
      Timestamp: data.Timestamp,
      TaskSid: data.TaskSid,
      TaskAttributes: data.TaskAttributes,
      TaskQueueName: data.TaskQueueName,
      TaskAssignmentStatus: data.TaskAssignmentStatus,
      TaskChannelUniqueName: data.TaskChannelUniqueName
    };
    console.info("Task list sync map data: ",mapData);
    
    await syncService.syncMaps(task_list_map)
      .syncMapItems(data.TaskSid).update({
        data: mapData,
      })
      .catch(async function (error) {
        if (error.status == '404') {
          await syncService.syncMaps(task_list_map)
            .syncMapItems.create({
              key: data.TaskSid,
              data: mapData,
              ttl: 0
            })
            .catch(error => {
              return "<Response>"+error+"</Response>"; 
            })
        } else {
          return "<Response>"+error+"</Response>"; 
        }
      });

      return "<Response>Success.</Response>";
  } catch (error) {
    return "<Response>"+error+"</Response>";             
  }
}

var remove_sync_task_map = async function(twilioAccount, data) {
  try {
    if (JSON.stringify(twilioAccount) === '{}') {
      return false;
    }
    let clientSync = await getTwilioClient(twilioAccount.account_sid,twilioAccount.authtoken)

    var syncService = clientSync.sync.v1.services(twilioAccount.sync_service_sid)

    console.info("Remove task list sync map item ", data.TaskSid)
    await syncService.syncMaps(task_list_map)
      .syncMapItems(data.TaskSid)
      .remove();
    return "<Response>Success.</Response>";
  } catch (error) {
    return "<Response>"+error+"</Response>";     
  }
}

module.exports = {
  twilio_status_callback: twilio_status_callback,
  make_leave_queue_request: make_leave_queue_request,
  update_twillio_call: update_twillio_call,
  update_callcener_task: update_callcener_task,
  worker_activity_update_request: worker_activity_update_request,
  insert_task_activity: insert_task_activity,
  process_inbound_call: process_inbound_call,
  process_inbound_call_destination_data: process_inbound_call_destination_data,
  get_dialing_code: get_dialing_code,
  remove_dialing_code: remove_dialing_code,
  add_dialing_code: add_dialing_code,
  generateRowId: generateRowId,
  make_location_leave_queue_request: make_location_leave_queue_request,
  sync_task_map: sync_task_map,
  remove_sync_task_map: remove_sync_task_map,
  getAccountFromSid: getAccountFromSid,
  parseCallRouteVariables: parseCallRouteVariables
};
