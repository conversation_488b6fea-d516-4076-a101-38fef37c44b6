{"name": "lambda-function-gateway-twillio", "version": "1.1.0", "description": "<PERSON><PERSON> twilio callbacks.", "main": "lambda.js", "config": {}, "scripts": {"test": "NODE_ENV=test jest", "test:integration": "jest --testPathPattern=test/integration -c test/jest.integration.config.js --ci --reporters=default --reporters=jest-junit --coverage", "test:unit": "NODE_ENV=test jest --testPathPattern=test/unit --coverage", "test:unit:conference-helper": "jest --testPathPattern=test/unit/conference-helper --coverage"}, "license": "Apache-2.0", "dependencies": {"@callpotential/CP-Text-Messaging-Services": "0.0.19", "@vendia/serverless-express": "^4.10.0", "aws-sdk": "^2.180.0", "body-parser": "^1.15.2", "card-validator": "8.1.1", "cors": "^2.8.1", "express": "^4.14.0", "got": "^8.3.1", "hot-shots": "^10.0.0", "lodash": "^4.17.4", "moment-timezone": "^0.5.11", "redis": "^4.2.0", "sprintf-js": "^1.0.3", "twilio": "^3.61.0"}, "devDependencies": {"eslint": "^8.21.0", "eslint-config-airbnb-base": "^15.0.0", "jest": "^29.3.1", "jest-junit": "^15.0.0", "mysql": "^2.18.1", "supertest": "^6.3.3", "xml2js": "^0.4.23"}, "engines": {"node": ">=16.0.0"}, "jest": {"testEnvironment": "node", "coveragePathIgnorePatterns": ["/node_modules/"], "collectCoverage": true, "coverageReporters": ["json", "html", "lcov"]}, "jest-junit": {"suiteName": "call lambda tests", "outputDirectory": "./Reports", "outputName": "/junit-call-lambda.xml", "uniqueOutputName": "false", "classNameTemplate": "{classname}-{title}", "titleTemplate": "{classname}-{title}", "ancestorSeparator": " › ", "usePathForSuiteName": "true"}}