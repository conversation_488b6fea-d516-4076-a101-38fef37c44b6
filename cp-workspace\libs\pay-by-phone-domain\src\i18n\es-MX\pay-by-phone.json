{"collections-prompt-pbp-allowed": "Para realizar un pago, presione 1. Para hablar con un gerente, presione 2. Para ser eliminado de nuestra lista, presione 9.", "collections-prompt-pbp-not-allowed": "Para hablar con un gerente, presione 2. Para ser eliminado de nuestra lista, presione 9.", "opt-out-success": "Su número de teléfono ha sido eliminado de nuestra lista.", "locale-select": "Para Espanol, presione {0.optionN<PERSON>ber}.", "total-balance": "Su saldo total es {0.totalBalance}. Para realizar un pago, presione 1. Para hablar con un gerente, presione 0.", "account-found": "Hemos localizado una cuenta que pertenece a {0.tenantName}, para la unidad número {1.unitName}. Su saldo total es {2.unitBalance}. Su próxima fecha de pago programada es el {3.paymentDate}. Para realizar un pago, presione 1. Para hablar con un gerente, presione 0.", "ask-prepay": "Presione el número del teclado que representa el número de pagos que le gustaría realizar. Por ejemplo, para pagar solo un mes de anticipación, presione 1, o, para pagar dos meses de anticipación, presione 2. Tenga en cuenta que el sistema automatizado no acepta pagos parciales.", "ask-prepay-zero-balance": "No puede pagar un saldo de cero dólares.", "prepay-selection": "Ha seleccionado a pagar {0.numPayments} meses por adelantado por un total de {1.prepayAmountDue}. Oprime el numero 1 para procesar su pago o oprime el numero 2 para hacer un cambio.", "ask-saved-cc": "Si desea utilizar una de sus tarjetas de credito que a usado previamente por favor oprima los ultimos 4 numeros de la tarjeta ahora o, para insertar una nueva tarjeta, presione 0 seguido de estrella.", "ask-for-number": "Ingrese el número de teléfono asociado con su cuenta o, para hablar con un gerente, presione 0 seguido del signo de número.", "account-not-found": "No podemos ubicar su cuenta.", "must-enter-ten-digits": "Debes ingresar el numero de telefono con codigo de area.", "multiple-accounts": "Encontramos varias cuentas que con su número de teléfono.", "pay-all": "Oprima 1 para pagar todas las unidades.", "account-select": "Si esta hablado acerca de la cuenta de {1.tenantName} en la unidad {2.unitName}, oprima {0.keyPress}.", "start-over": "Para empezar de nuevo, oprima 9.", "speak-manager": "Para hablar con un gerente, presione 0 seguido del signo de número.", "not-allowed": "El pago por telefono no esta permitido en esta cuenta.", "max-retry": "<PERSON> sentimos, usted a alcanzado el numero maximo de intentos.", "card-not-found": "No hemos podido encontrar la tarjeta de credito con ese numero.", "amount-due": "El monto total que se cargará a su tarjeta es de {0.paymentAmount}.", "enter-cc": "Ingrese su número de tarjeta, seguido de la estrella.", "enter-expiry": "Ingrese la fecha de vencimiento en la tarjeta utilizando los dos dígitos del mes y los dos últimos dígitos del año, seguido de la estrella.", "enter-ccv": "Ingrese el codigo de seguridad, seguido de la estrella.", "enter-zip": "Ingrese el código postal, seguido de la estrella.", "confirm-payment": "Para confirmar la información ingresada y procesar su pago, oprime el 1.", "invalid-input": "Entrada ivalida.", "payment-under-process": "Por favor espere. Su pago esta siendo procesado.", "payment-success": "Su pago ha sido procesado.", "payment-fail": "No hemos podido procesar su pago. Por favor, espere mientras le conectamos con un gerente.", "cc-confirm": "El número que ingresaste es {0.enteredNumber}. Para confirmar que el número es correcto, presione 1, o, para volver a ingresar el número, presione 2.", "expiration-confirm": "La fecha de vencimiento que ingresó es {0.enteredDate}. Para confirmar que esto es correcto, presione 1, o, para volver a ingresar una fecha de vencimiento, presione 2.", "security-code-confirm": "El código de seguridad que ingresaste es {0.enteredCvc}. Para confirmar que esto es correcto, presione 1, o, para volver a ingresar un código de seguridad, presione 2.", "zip-confirm": "El código postal que ingresó es {0.enteredZip}. Para confirmar que esto es correcto. presione 1, o, para volver a ingresar un código postal, presione 2.", "wait-for-account-fetch": "Espere mientras recopilamos los detalles de su cuenta.", "could-not-find-card": "No hemos podido encontrar la tarjeta de credito con ese numero."}