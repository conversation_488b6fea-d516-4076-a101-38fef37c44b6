const AWS = require('aws-sdk');
AWS.config.update({ region: process.env.AWS_REGION || 'us-west-2' });
const config = require('./config');
const ConferenceHelper = require('./shared/conference-helper');
const EventContextHelper = require('./shared/event-context-helper');
const bugsnagService = require('../libraries/bugsnag-service');

async function handler(event, context) {
  if (event.source === 'aws.events') return context.done(); // keep warm!

  // Track all conference events
  bugsnagService.leaveBreadcrumb('Conference event handler triggered', {
    hasEvent: !!event,
    eventKeys: event ? Object.keys(event) : [],
    queryStringParameters: event?.queryStringParameters,
    body: event?.body,
    httpMethod: event?.httpMethod,
    path: event?.path
  });

  const eventContext = await EventContextHelper.prepareEventContext(event);

  bugsnagService.leaveBreadcrumb('Event context prepared', {
    statusCallbackEvent: eventContext?.statusCallbackEvent,
    hasParams: !!(eventContext?.params),
    paramsKeys: eventContext?.params ? Object.keys(eventContext.params) : [],
    recordingStatus: eventContext?.params?.RecordingStatus,
    customerCallSid: eventContext?.params?.Customer
  });

  await ConferenceHelper.handleRecording(eventContext, config);

  return processEvent(eventContext);
}

async function processEvent(eventContext) {
  const { statusCallbackEvent} = eventContext;
  
  eventContext.statusCallbackEvent = statusCallbackEvent;

  if (ConferenceHelper.eventHandlers[statusCallbackEvent]) {
    await ConferenceHelper.eventHandlers[statusCallbackEvent](eventContext);
  }

  return EventContextHelper.response();
}

module.exports = {
  handler,
};
