import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext, PayByPhoneStateHandlerResponse } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { FinalPayAmountPrompt } from './FinalPayAmountPrompt';
import { Ledger, Locale } from '@cp-workspace/shared';

describe('FinalPayAmountPrompt', () => {
  let finalPayAmountPrompt: FinalPayAmountPrompt;
  let context: PayByPhoneStateContext;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FinalPayAmountPrompt],
    }).compile();

    finalPayAmountPrompt = module.get<FinalPayAmountPrompt>(FinalPayAmountPrompt);

    context = {
      storage: {
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.FinalPayAmountPrompt,
        locationId: 123,
        locale: Locale.English,
        transferToAgentUrl: '',
        convenienceFee: 0,
        selectedUnits: [{
          amount_owed: '100.12',
        } as Ledger,
          {
            amount_owed: '150.12',
          } as Ledger
        ],
        totalBalance: 250.24,
        totalAmountDue: 250.24
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 123,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should calculate total amount due and prompt for payment confirmation', async () => {
      const response: PayByPhoneStateHandlerResponse = await finalPayAmountPrompt.handler(context);

      const totalAmountDue = "250.24";
      const gatherOptions = {
        numDigits: 1,
        method: 'POST',
        timeout: 10,
      };

      expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith(gatherOptions, [
        { messageId: "pay-by-phone.amount-due", locale: Locale.English, i18nOptions: { args: [{ paymentAmount: totalAmountDue }] } },
        {messageId: "pay-by-phone.confirm-payment", locale: Locale.English}
      ])

      expect(response.nextState).toBe(PayByPhoneState.FinalPayAmountConfirm);
    });
  });
});
