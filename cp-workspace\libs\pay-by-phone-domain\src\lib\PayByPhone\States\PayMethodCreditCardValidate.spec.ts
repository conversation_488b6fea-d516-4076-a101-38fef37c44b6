import { Test, TestingModule } from '@nestjs/testing';
import { PayMethodCreditCardValidate } from './PayMethodCreditCardValidate';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { Locale } from '@cp-workspace/shared';


describe('PayMethodCreditCardValidate', () => {
  let service: PayMethodCreditCardValidate;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PayMethodCreditCardValidate,
        {
          provide: 'Services',
          useValue: {
            coreService: {
              encodePaymentToken: jest.fn(),
            },
          },
        },
      ],
    }).compile();

    service = module.get<PayMethodCreditCardValidate>(
      PayMethodCreditCardValidate
    );
    service.services = module.get('Services');
  });

  it('should return nextState as PayMethodCreditCardPrompt when credit card number is invalid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1234',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodCreditCardPrompt);
  });

  it('should return nextState as PayMethodCreditCardConfirm when credit card number is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '****************',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodCreditCardConfirm);
  });

  it('should call sayInLocale with correct arguments when credit card number is invalid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1234',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;


    await service.handler(context);

    expect(context.twilioResponse.sayInLocale).toHaveBeenCalledWith({
      messageId: 'pay-by-phone.invalid-input',
      locale: context.storage.locale
    });
  });

  it('should call encodePaymentToken with correct argument when credit card number is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '****************',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;


    await service.handler(context);

    expect(
      service.services.coreService.encodePaymentToken
    ).toHaveBeenCalledWith({
      cardNumber: context.request.Digits,
    });
  });

  it('should call gatherWithLocaleSay with correct arguments when credit card number is valid', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '****************',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gatherWithLocaleSay: jest.fn() } as any,
      storage: { locale: Locale.English } as any,
    } as any;


    await service.handler(context);

    expect(context.twilioResponse.gatherWithLocaleSay).toHaveBeenCalledWith({
      method: 'POST',
      numDigits: 1,
      timeout: 10
    }, [{
      messageId: "pay-by-phone.cc-confirm",
      locale: context.storage.locale,
      i18nOptions: {args: [{enteredNumber: context.request.Digits?.split('').join(' ')}]}
    }]);
  });
});
