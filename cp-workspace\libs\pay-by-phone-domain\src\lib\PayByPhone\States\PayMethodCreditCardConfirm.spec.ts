import { PayMethodCreditCardConfirm } from './PayMethodCreditCardConfirm';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

describe('PayMethodCreditCardConfirm', () => {
  let service: PayMethodCreditCardConfirm;

  beforeEach(() => {
    service = new PayMethodCreditCardConfirm();
  });

  it('should transition to PayMethodExpirationPrompt when confirmation selection is 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '1',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodExpirationPrompt);
  });

  it('should transition to PayMethodCreditCardPrompt when confirmation selection is not 1', async () => {
    const context: PayByPhoneStateContext = {
      request: {
        Digits: '2',
        CallSid: '',
        LocationId: 0,
        TransferToAgentUrl: '',
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
      storage: { locale: 'en' } as any,
    } as any;

    const result = await service.handler(context);

    expect(result.nextState).toBe(PayByPhoneState.PayMethodCreditCardPrompt);
  });
});
