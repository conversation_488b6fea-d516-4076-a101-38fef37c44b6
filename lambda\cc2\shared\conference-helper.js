const TwilioConstants = require("./twilio-constants");
const cpapiClient = require("../cpapi_client");
let config;
if (process.env.NODE_ENV === 'test') {
  config = require('../config.sample');
} else {
  config = require('../config');
}
const CpapiHelper = require("./cpapi-helper");
const EventContextHelper = require("./event-context-helper");
const TwilioHelper = require("./twilio-helper");

const eventHandlers = {
  "participant-join": handleParticipantJoin,
  "participant-leave": handleParticipantLeave,
  "conference-start": handleConferenceStart,
  "conference-end": handleConferenceEnd,
  "warm-transfer-start": handleWarmTransferStart,
  "warm-transfer-continue": handleWarmTransferContinue,
  "warm-transfer-cancel": handleWarmTransferCancel,
};

async function handleParticipantJoin(context) {
  const { workspace, task, twilioClient, params } = context;

  // if (task.assignmentStatus === TwilioConstants.TaskAssignmentStatus.Completed) {
  //   console.log("Task already completed, ignoring participant-join event");
  //   await completeConference(context, params.ConferenceSid);
  // }

  /**
   * If the call is a transfer, mark the transfer call as 'joined' under the task attributes
   * and update the task attributes.
   */
  let taskAttributes = JSON.parse(task.attributes);
  const transferCallStatus = taskAttributes.conference?.participants?.transfer?.status;

  if (transferCallStatus === 'confirmed') {
    // Update the transfer jey in the task attributes to have a status of 'joined'
    taskAttributes.conference.participants.transfer.joined = true;
    await updateTwilioTaskAttributes(taskAttributes, task, workspace);
  }

  /**
   *
   * CALL CENTER AGENT CALL HANDLING:
   * When an inbound call center call is received, an outbound call to an agent is made.
   * When the agent call is answered, the agent is added to a conference.
   * Upon being added to the conference, Twilio will fire a `participant-join` event
   *
   * We handle that event here.
   *
   * For calls to agents with non-SIP addresses, accept the task reservation.
   * Since the agent call is now in the conference, add customer call to the conference.
   *
   * SPECIAL HANDLING FOR SIP ADDRESSES:
   * For calls to agents with SIP addresses, accept the task reservation only if the
   * the sip address is a Twilio SIP address.  Otherwise, the reservation will be accepted
   * based on an external webhook event.
   *
   */

  // If participant label includes agent, then conference in customer
  if (
    params.connectType === TwilioConstants.ConnectType.Phone &&
    params.ParticipantLabel?.includes("agent")
  ) {

    /**
     * To support the SPECIAL HANDLING FOR SIP ADDRESSES, we need to know the
     * agent's SIP address type (Twilio or External).  This is communicated via
     * the `agentToType` query string parameter.
     * If `agentToType` is not available, then we will accept the reservation
     * and dial the customer into the conference because this event represents
     * a non-SIP agent joining the conference.
     */
    const { agentToType } = params;
    let acceptCall = true;
    if (agentToType && agentToType === "sip-external") {
      acceptCall = false;
    }

    if (acceptCall) {
      /**
       * Accept the task reservation
       */
      await TwilioHelper.updateReservationStatus(twilioClient, workspace, params.FriendlyName, params.reservationSid, params.CallSid);

      /**
       * Dial the customer into the conference
       */
      await TwilioHelper.dialCustomerInConference(context.twilioClient, params.customerCallSid, params.FriendlyName);
    }

  }
}

async function getConferenceByFriendlyName(twilioClient, friendlyName) {
  const conferences = await twilioClient.conferences.list({friendlyName, limit: 1});
  if (conferences.length) {
    return conferences[0];
  }
  return null;
}

async function writeConferenceParticipantDetailsToTaskAttributes(context) {
  const { params, task, twilioClient, workspace } = context;
  const taskAttributes = JSON.parse(task.attributes);
  const conference = await getConferenceByFriendlyName(twilioClient, params.FriendlyName);
  const participants = await getParticipants(
    twilioClient,
    conference.sid
  );

  try {


    let customerSid;
    if (taskAttributes.conference) {
      /**
       * If the conference object is present in the task attributes, then we can
       * assume that the customer call sid is present in the conference object.
       *
       * KNOWN QUIRK:
       * There is one moment in the call flow, for call transfers, where the value of
       * `taskAttributes.conference.participants.customer` is not an object with
       * a `callSid` property.  Instead, it is a string that represents the call sid.
       *
       * Presently, we will check for this condition and assign the value of the
       * `customerSid` variable accordingly.
       */
      if (taskAttributes.conference?.participants?.customer?.callSid) {
        customerSid = taskAttributes.conference.participants.customer.callSid;
      } else if (taskAttributes.conference?.participants?.customer) {
        customerSid = taskAttributes.conference.participants.customer;
      }
    }

    /**
     * If the customerSid value is not found in the "conference" object, then we will
     * attempt to locate the customerSid value, elsewhere, in the task attributes.
     */
    if (!customerSid) {
      if (taskAttributes.outboundCustomerCallSid) {
        /**
         * Outbound calls are expected to write the customer call sid to the
         * `outboundCustomerCallSid` property in the task attributes.
         */
        customerSid = taskAttributes.outboundCustomerCallSid;
      } else if (taskAttributes.direction === "inbound" && taskAttributes.call_sid) {
        /**
         * The last resort is to use the `call_sid` property in the task attributes
         * if the call directionis "inbound".
         */
        customerSid = taskAttributes.call_sid;
      } else {
        throw new Error("No customer call found in task attributes");
      }
    }

    /**
     * Given the known customerSid value and the list of participants,
     * locate the participant whose callSid matches the customerSid and assign
     * this participant to the customerParticipant variable.
     *
     * The first participant that does not match the customer callSid will be
     * assigned to the workerParticipant variable.
     *
     * These variables will be assigned using a single iteration over the
     * participants array.
     */
    let customerParticipant;
    let workerParticipant;
    participants.forEach((participant) => {
      if (participant.callSid === customerSid) {
        customerParticipant = participant;
      } else {
        workerParticipant = participant;
      }
    });
    // console.log("FOUND PARTICIPANTS", { customerParticipant, workerParticipant });

    const [customerCall, workerCall] = await Promise.all([
      getCallInfo(customerParticipant.callSid, twilioClient),
      getCallInfo(workerParticipant.callSid, twilioClient),
    ]);

    taskAttributes.conference = {
      sid: conference.sid,
      participants: {
        customer: {
          callSid: customerCall.sid,
          from: customerCall.from,
          to: customerCall.to,
        },
        worker: {
          callSid: workerCall.sid,
          from: workerCall.from,
          to: workerCall.to,
        },
      },
    };
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
    throw e;
  }

  // console.log("CONFERENCE START:", taskAttributes.direction);
  // console.log("CONF DETAILS:", JSON.stringify(taskAttributes.conference, null, 2));

  await updateTwilioTaskAttributes(taskAttributes, task, workspace);
  const updatedTask = await workspace.tasks(task.sid).fetch();
  const updatedTaskAttributes = JSON.parse(updatedTask.attributes);
  return updatedTaskAttributes;
}

async function handleConferenceStart(context) {
  const { params } = context;

  try {
    await writeConferenceParticipantDetailsToTaskAttributes(context);
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
  }

  await CpapiHelper.updateReport(params).catch((err) =>
    console.error(context.statusCallbackEvent, err)
  );
}

async function handleConferenceEnd(context) {
  const { params, task, twilioClient, workspace } = context;
  // console.debug("handleConferenceEnd", context);
  const taskAttributes = JSON.parse(task.attributes);

  let overideRecCheck = false;
  if (taskAttributes.outboundCustomerCallSid) {
    let customerCallInfo = await getCallInfo(
      taskAttributes.outboundCustomerCallSid,
      twilioClient
    );
    if (
      "status" in customerCallInfo &&
      ["canceled", "failed", "busy", "no-answer"].includes(
        customerCallInfo.status
      )
    ) {
      overideRecCheck = true;
    }
  }

  /**
   * Since the conference has ended, we need to update the task to a completed state.
   *
   * NOTE:
   * Completing the task, here, used to be done only if the task status was 'assigned'.
   * However, we have found that the task status is not always 'assigned' when the conference
   * ends.  In some cases, the status will be "reserved", for example.  This can happen if the
   * callers disconnect at the same time.
   *
   * Therefore, we are now completing the task regardless of the task status.  We are doing
   * this with the understanding that by the time the conference ends, the task should be
   * in a state where it can be completed.
   */
  await updateTwilioTaskMarkAsCompleted(task, workspace);

  if (params.connectType === TwilioConstants.ConnectType.Phone) {
    try {
      /*
        CCC-373
        We need to determine if the worker's current activity is "Offline" which will
        happen in the case of a call getting interrupted for an unknown reason such
        as a browser window closing while in the middle of a call where the agent
        is connected via the "Computer" method.

        If the worker is currently Offline, we do not want to move the worker to Cool-Down
      */
      const currWorker = await workspace.workers(params.WorkerSid).fetch();
      const isOffline = currWorker.activityName === "Offline";
      const isOnCall = currWorker.activityName === "On-call";

      // addresses CCC-105 and CCC-123 -- only move to cooldown when NOT TTL Exceeded and NOT hangup
      // SequenceNumber 2 with conference-end event will be due to phone/voip not being answered
      // alreadyInCoolDown: In call transfer, if worker already left conference and
      // moved to cool-down (in participant-leave event), then do not move to cool-down again
      if (
        isOnCall &&
        !isOffline &&
        task.reason !== "Task TTL Exceeded" &&
        task.reason !== "hangup" &&
        params.SequenceNumber != 2 &&
        !taskAttributes.alreadyInCoolDown
      ) {
        await updateWorkerActivityTo("Cool-Down", workspace, params);
      }
    } catch (e) {
      console.error(context.statusCallbackEvent, e);
    }
  }

  try {
    params.locationId = params.locationId || taskAttributes.location_id;
    await updateComputedCallDuration(twilioClient, params, taskAttributes);
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
  }


  const callSid = params.callSid || params.CustomerCallSid;
  try {
    if (callSid) {
      const call = await twilioClient.calls(callSid).fetch();
      params.callDuration = call.duration;
      params.recordingUrl = "";
      params.overideRecCheck = overideRecCheck;

      await CpapiHelper.updateReport(params);
    }
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
  }

  // CPAPI-2087 Check if recording is enabled.
  // When recording is enabled audit log will be added from recording status callback url
  // Otherwise audit log will be added on conference end event
  // console.log("RECORD OUTGOING", params.record_outgoing, overideRecCheck);
  if (overideRecCheck || params.record_outgoing != 1) {
    const twilioAccount =
      "AccountSid" in params
        ? await EventContextHelper.dynamoQuery(params.AccountSid)
        : await EventContextHelper.twilioCredentials(params.authToken);

    params.authToken = twilioAccount.authtoken;

    try {
      if (callSid) {
        const callClient = new cpapiClient.callClient(
          config.db.serviceTokens.readWrite
        );
        const callData = await callClient.getData(
          `call/${params.locationId}/${callSid}`
        );
        // console.log("CALL DATA", callData);
        // console.log("PARAMS", params);
        await CpapiHelper.createAuditLog(params, callData);
      } else {
        console.log("No customer call sid found while creating audit log");
      }
    } catch (e) {
      console.error(context.statusCallbackEvent, e);
    }
  }

  // Terminate conference and mark as completed. This applies when the agent makes a call
  // and this hangs up few seconds later and the call is still running but the customer
  // haven't answered.
  await completeConference(context, params.ConferenceSid);

  // If only left one participant inside de conference room, then change the
  // status of the own call.
  const participants = await getParticipants(
    twilioClient,
    params.ConferenceSid
  );
  if (participants.length === 1) {
    console.log("ONLY ONE PARTICIPANT");
    await completeCall(context, participants[0].callSid);
  }
}

async function completeConference(context, conferenceSid) {
  const { twilioClient } = context;
  try {
    await twilioClient
    .conferences(conferenceSid)
    .update({ status: TwilioConstants.TaskAssignmentStatus.Completed });
  } catch(e) {
    if (!is400Error(e)) {
      console.error(context.statusCallbackEvent, e);
    }
  }
}

async function completeCall(context, callSid) {
  const { twilioClient } = context;
  console.log("COMPLETE CALL", callSid);
  try {
    await twilioClient
    .calls(callSid)
    .update({ status: "completed" });
  } catch(e) {
    console.log(`Error completing call ${callSid}`, e);
    if (!is400Error(e)) {
      console.error(context.statusCallbackEvent, e);
    }
  }
}

/**
 * CONFERENCE EVENT HANDLER: "PARTICIPANT LEAVE"
 *
 * This event is fired when a participant leaves the conference.
 *
 * We use this event to understand when a conference should be ended.
 *
 * When this event relates to CUSTOMER:
 * - End the call associated with this event (and therefore, the conference)
 *
 * When this event shows that there no more participants in the conference:
 * - End the call associated with this event (and therefore, the conference)
 *
 * When this event shows that there is one more participant in the conference:
 * - End the last participant's call (and therefore, the conference)
 *
 */
async function handleParticipantLeave(context) {
  // console.debug("handleParticipantLeave", context);
  const { params, task, twilioClient, workspace } = context;
  const taskAttributes = JSON.parse(task.attributes);
  const customerCallSid = taskAttributes.conference?.participants?.customer?.callSid || taskAttributes.conference?.participants?.customer;

  const { ParticipantCallStatus, ParticipantLabel } = params;

  /**
   * Get the participants in the conference at the time
   * of this event.
   */
  const participants = await getParticipants(
    twilioClient,
    params.ConferenceSid
  );

  /**
   * Create a map of the active participant call sids
   */
  const activeParticipantCallSids = {};
  if (customerCallSid) {
    activeParticipantCallSids[customerCallSid] = true;
  }
  participants.forEach((participant) => {
    activeParticipantCallSids[participant.callSid] = true;
  });

  console.log("PARTICIPANT LEFT: PARTICIPANTS", {
    participants,
    activateCallSids: Object.keys(activeParticipantCallSids)
  });

  const customerParticipantLeft = taskAttributes.conference?.participants?.customer?.callSid === params.CallSid;
  const agentParticipantLeft = taskAttributes.conference?.participants?.worker?.callSid === params.CallSid;
  const transferParticipantLeft = taskAttributes.conference?.participants?.transfer?.callSid === params.CallSid;
  const noParticipantsLeft = participants.length === 0;
  const oneParticipantLeft = participants.length === 1;
  const transferCallStatus = taskAttributes.conference?.participants?.transfer?.status;

  let endConferenceCall = false;

  /**
   * Scenarios for ending the conference call if/when a transfer is in progress:
   * - Transfer call status is unconfirmed and agent or customer left the conference
   * - Transfer call status is confirmed or cancelled and no participants are left in the conference
   */
  if (customerParticipantLeft) {
    console.log("[Participant Left] - Ending conference call because customer left the conference");
    endConferenceCall = true;
  }
  else if (transferCallStatus) {
    switch (transferCallStatus) {
      case 'unconfirmed':
        if (agentParticipantLeft || customerParticipantLeft) {
          console.log("[Merge Call Unconfirmed] - Ending conference call because agent or customer left the conference");
          endConferenceCall = true;
        }
        break;
      case 'confirmed':
        if (participants.length < 2 && transferParticipantLeft) {
          console.log("[Merge Call Confirmed] - Ending conference call because transfer participant left and one participant remains in the conference");
          endConferenceCall = true;
        }
        break;
      case 'cancelled':
        if (participants.length < 2) {
          console.log("[Merge Call Cancelled] - Ending conference call because one or no participants are left in the conference");
          endConferenceCall = true;
        }
        break;
    }
  } else {
    /**
     * Scenarios for ending the conference call:
     * - Customer left the conference
     * - No participants are left in the conference
     * - One participant is left in the conference
     */
    if (noParticipantsLeft || oneParticipantLeft) {
      endConferenceCall = true;
    }
  }

  if (endConferenceCall) {
    // End all remaining participant calls
    Object.keys(activeParticipantCallSids).forEach(async (callSid) => {
      await completeCall(context, callSid);
    });

    // Complete the conference to end the conference call
    await completeConference(context, params.ConferenceSid);
  }

  // Place worker in cool-down since worker has left the conference
  console.log("Changing worker activity to cool-down");
  if (ParticipantCallStatus !== "in-progress" && ParticipantLabel && ParticipantLabel.includes('agent')) await updateWorkerActivityTo("Cool-Down", workspace, params);

  console.log("PARTICIPANT LEFT: FLAGS", {
    customerParticipantLeft,
    noParticipantsLeft,
    oneParticipantLeft,
    endParticipantCall: endConferenceCall,
    transferCallStatus
  });
}

async function handleWarmTransferStart(context) {
  const { twilioClient, params, task, workspace } = context;
  const { FriendlyName } = params;
  const conference = twilioClient.conferences(FriendlyName);
  let taskAttributes = JSON.parse(task.attributes);

  try {

    taskAttributes = await writeConferenceParticipantDetailsToTaskAttributes(context);

    const from =
      taskAttributes.direction === "inbound"
        ? taskAttributes.conference.participants.customer.to
        : taskAttributes.conference.participants.customer.from;

    const transfer = await conference.participants.create({
      from,
      to: params.to,
      startConferenceOnEnter: true,
      endConferenceOnExit: false,
    });

    taskAttributes.conference.participants.transfer = {
      callSid: transfer.callSid,
      from,
      to: params.to,
      status: 'unconfirmed'
    };
    await updateTwilioTaskAttributes(taskAttributes, task, workspace);
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
  } finally {
    await updateParticipantOnHoldStatus(true, twilioClient, taskAttributes.conference.sid, taskAttributes.conference.participants.customer.callSid);
  }
}

async function handleWarmTransferContinue(context) {
  const { twilioClient, task, workspace } = context;
  let taskAttributes = JSON.parse(task.attributes);

  taskAttributes.conference.participants.transfer.status = 'confirmed';

  try {
    await updateTwilioTaskAttributes(taskAttributes, task, workspace);
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
  }

  try {
    await updateParticipantOnHoldStatus(false, twilioClient, taskAttributes.conference.sid, taskAttributes.conference.participants.customer.callSid);
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
  }
}

async function handleWarmTransferCancel(context) {
  const { twilioClient, task, workspace } = context;
  let taskAttributes = JSON.parse(task.attributes);
  const conference = twilioClient.conferences(taskAttributes.conference.sid);

  taskAttributes.conference.participants.transfer.status = 'cancelled';

  try {
    await updateTwilioTaskAttributes(taskAttributes, task, workspace);
  } catch (e) {
    console.error(context.statusCallbackEvent, e);
  }

  try {
    if (taskAttributes.conference?.participants?.transfer?.callSid) {
      await conference
      .participants(taskAttributes.conference.participants.transfer.callSid)
      .remove();
    }
  } catch (e) {
    if (!e.message.includes("Participant is not connected")) {
      console.error(context.statusCallbackEvent, e);
    }
  } finally {
    await updateParticipantOnHoldStatus(false, twilioClient, taskAttributes.conference.sid, taskAttributes.conference.participants.customer.callSid);
  }
}

async function handleRecording(context, config) {
  const { params, twilioClient } = context;

  // Add Bugsnag service import if not already present
  const bugsnagService = require('../../libraries/bugsnag-service');

  // Track all recording events, not just completed ones
  bugsnagService.leaveBreadcrumb('Conference recording event received', {
    recordingStatus: params.RecordingStatus,
    hasRecordingUrl: !!(params.RecordingUrl),
    recordingUrl: params.RecordingUrl,
    customerCallSid: params.Customer,
    recordingSid: params.RecordingSid,
    allParamsKeys: Object.keys(params)
  });

  if (
    "RecordingStatus" in params &&
    params.RecordingStatus === TwilioConstants.RecordingStatus.Completed
  ) {
    bugsnagService.leaveBreadcrumb('Conference recording completed - processing', {
      customerCallSid: params.Customer,
      recordingUrl: params.RecordingUrl,
      recordingSid: params.RecordingSid
    });

    params.callSid = params.Customer;

    try {
      if (params.RecordingDuration > 0) {
        params.callDuration = params.RecordingDuration;
      } else {
        const call = await twilioClient.calls(params.callSid).fetch();
        params.callDuration = call.duration;
      }

      params.recordingUrl = params.RecordingUrl;
      params.billingDuration = params.callDuration;

      await CpapiHelper.updateReport(params);

      const callClient = new cpapiClient.callClient(
        config.db.serviceTokens.readWrite
      );

      const callData = await callClient.getData(
        `call/${params.locationId}/${params.Customer}`
      );
      // console.log("HANDLE RECORDING", params, callData);

      await CpapiHelper.updateRecordingDetails(params);
      await CpapiHelper.createAuditLog(params, callData);

      bugsnagService.leaveBreadcrumb('Conference recording processing completed successfully', {
        customerCallSid: params.Customer,
        recordingUrl: params.RecordingUrl
      });
    } catch (e) {
      console.log(e);
      bugsnagService.notify(e, {
        severity: 'error',
        metadata: {
          operation: 'handleRecording',
          customerCallSid: params.Customer,
          locationId: params.locationId,
          recordingUrl: params.RecordingUrl,
          params: params
        }
      });
    }
    return response();
  } else {
    // Track when recording events are received but not processed
    bugsnagService.leaveBreadcrumb('Conference recording event not processed', {
      recordingStatus: params.RecordingStatus,
      hasRecordingStatus: "RecordingStatus" in params,
      isCompleted: params.RecordingStatus === TwilioConstants.RecordingStatus.Completed,
      customerCallSid: params.Customer,
      reason: !("RecordingStatus" in params) ? 'No RecordingStatus in params' :
              params.RecordingStatus !== TwilioConstants.RecordingStatus.Completed ? 'RecordingStatus not completed' : 'Unknown'
    });
  }
}

async function updateComputedCallDuration(
  twilioClient,
  params,
  taskAttributes
) {
  // console.log('UPDATE COMPUTED DURATION', params);

  /*
    CP-9508
    For call center calls, we must locate the worker call duration and
    the customer call duration.  Record the smaller of the two durations.
  */

  if (taskAttributes.conference?.participants) {

    let callWorker = null;
    const workerCallSid = taskAttributes.conference?.participants?.worker?.callSid || taskAttributes.conference?.participants?.worker;
    if (workerCallSid) {
      callWorker = await twilioClient
        .calls(workerCallSid)
        .fetch();
    } else {
      console.log('updateComputedCallDuration: No worker callSid found');
    }

    let callCustomer = null;
    const customerCallSid = taskAttributes.conference?.participants?.customer?.callSid || taskAttributes.conference?.participants?.customer;
    if (customerCallSid) {
      callCustomer = await twilioClient
        .calls(customerCallSid)
        .fetch();
    } else {
      console.log('updateComputedCallDuration: No customer callSid found');
    }

    const durationWorker = callWorker != null ? Number(callWorker.duration) : NaN;
    const durationCustomer = callCustomer != null ?  Number(callCustomer.duration) : NaN;

    let duration = 0;
    try {
      const recordings = await twilioClient.recordings.list({
        callSid: workerCallSid,
        limit: 1,
      });

      if (recordings.length && recordings[0].status === TwilioConstants.RecordingStatus.Completed && recordings[0].duration > 0) {
        duration = recordings[0].duration;
      }
    } catch (e) {
      console.log("Could not get recording duration", e);
    }

    if (!duration) {
      if (callWorker && callCustomer) {
        duration = Math.min(durationWorker, durationCustomer);
      } else if (callWorker) {
        duration = durationWorker;
      } else if (callCustomer) {
        duration = durationCustomer;
      }
    }

    params.callDuration = duration;

    if (callWorker && callCustomer) {

      const callInitiator = durationCustomer > durationWorker ? callCustomer : callWorker;
      params.billingDuration = durationCustomer > durationWorker ? durationCustomer : durationWorker;
      params.callSid = params.callSid ? params.callSid : callInitiator.sid;

    } else if (callWorker) {

      params.billingDuration = durationWorker;
      params.callSid = params.callSid ? params.callSid : callWorker.sid;

    } else if (callCustomer) {

      params.billingDuration = durationCustomer;
      params.callSid = params.callSid ? params.callSid : callCustomer.sid;

    }

  } else {
    params.callDuration = 0;
  }

  if (params.callSid) {
    await CpapiHelper.updateOutboundCallLogs(params, params.callSid);
  }
}

const getParticipants = async (twilioClient, conferenceSid) =>
  await twilioClient.conferences(conferenceSid).participants.list();


const updateParticipantOnHoldStatus = async (
  status,
  twilioClient,
  conferenceSid,
  participantCallSid
) => {
  await twilioClient.conferences(conferenceSid)
    .participants(participantCallSid)
    .update({ hold: status, holdUrl: 'https://twimlets.com/holdmusic?Bucket=com.twilio.music.soft-rock' });
}

const getCallInfo = async (callSid, twilioClient) =>
  await twilioClient.calls(callSid).fetch();

const updateTwilioTaskMarkAsCompleted = async (task, workspace) => {
  let taskUpdate = TwilioConstants.TaskAssignmentStatus.Completed;
  if (
    task.assignmentStatus !== TwilioConstants.TaskAssignmentStatus.Assigned &&
    task.assignmentStatus !== "wrapping"
  )
    taskUpdate = TwilioConstants.TaskAssignmentStatus.Canceled;

  try {
    return await workspace
      .tasks(task.sid)
      .update({ assignmentStatus: taskUpdate, reason: "Call completed" });
  } catch (error) {
    console.error(`Failed to update task ${task.sid}: ${error}`);
  }
};

const updateTwilioTaskAttributes = async (attributes, task, workspace) => {
  await workspace
    .tasks(task.sid)
    .update({ attributes: JSON.stringify(attributes) });
};

const updateWorkerActivityTo = async (activityName, workspace, params) => {
  const activity = (
    await workspace.activities.list({ friendlyName: activityName })
  )[0];
  return workspace
    .workers(params.WorkerSid)
    .update({ activitySid: activity.sid });
};

function response(statusCode = 200, message = "ok") {
  return {
    statusCode,
    body: JSON.stringify({
      message,
    }),
  };
}

const is400Error = (e) => e.status >= 400 && e.status < 500;

module.exports = {
  handleParticipantJoin,
  handleRecording,
  eventHandlers,
  handleConferenceEnd,
  handleParticipantLeave,
  updateWorkerActivityTo,
  handleConferenceStart,
  updateComputedCallDuration,
  getConferenceByFriendlyName,
  writeConferenceParticipantDetailsToTaskAttributes,
  handleWarmTransferStart,
  handleWarmTransferContinue,
  handleWarmTransferCancel,
  response,
};
