import { Test, TestingModule } from '@nestjs/testing';
import { PayByPhoneStateContext } from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';
import { FinalPayAmountSubmitted } from './FinalPayAmountSubmitted';
import { Locale, PaymentData, PaymentToken } from '@cp-workspace/shared';

describe('FinalPayAmountSubmitted', () => {
  let finalPayAmountSubmitted: FinalPayAmountSubmitted;
  let context: PayByPhoneStateContext;
  const mockLocation = { save_cc: true, convenience_fee_phone: 5 };
  const mockPaymentToken: PaymentToken = {
    cardNumber: '1234567890123456',
    securityCode: '123',
    postalCode: '12345',
    expiration: '1234',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FinalPayAmountSubmitted],
    }).compile();

    finalPayAmountSubmitted = module.get<FinalPayAmountSubmitted>(FinalPayAmountSubmitted);
    finalPayAmountSubmitted.services = {
      coreService: {
        decodePaymentToken: jest.fn().mockResolvedValue({ cardNumber: '1234567890123456', securityCode: '123', postalCode: '12345', expiration: '12/34' }),
      } as any,
      locationService: {
        getLocationConfiguration: jest.fn().mockResolvedValue(mockLocation),
      } as any,
      integrationService: {
        createPaymentHash: jest.fn().mockResolvedValue({ hash: 'hash123' }),
        getPaymentHashDetails: jest.fn().mockResolvedValue({ items: [{ payment_id: 'payment123', source: 'source123' }] }),
        makeTenantBulkPayment: jest.fn().mockResolvedValue({ data: { success: true }, status: 201 }),
        makeTenantPayment: jest.fn().mockResolvedValue({ data: { success: true }, status: 201 }),
      } as any,
      dataDogService: {
        incrementCounter: jest.fn(),
        recordGauge: jest.fn(),
        getBucket: jest.fn(),
      } as any,
    } as any;

    context = {
      storage: {
        payByPhoneStartTime: Date.now(),
        origin: 'collections',
        retryCount: 0,
        payByPhoneAllowed: true,
        state: PayByPhoneState.FinalPayAmountSubmitted,
        locationId: 123,
        tenantId: 'tenant123',
        locale: Locale.English,
        transferToAgentUrl: '',
        paymentToken: mockPaymentToken,
        selectedUnits: [{ ledger_id: 1, unit_id: 'unit123', tenant_id: 'tenant123', amount_owed: '100.00' } as any],
        prepayMonths: undefined,
        useSavedCardId: undefined,
        convenienceFee: 5,
        totalBalance: 0,
        totalAmountDue: 0
      },
      request: {
        Digits: '',
        CallSid: '',
        LocationId: 123,
        TransferToAgentUrl: ''
      },
      twilioResponse: { sayInLocale: jest.fn(), gather: jest.fn() } as any,
    };
  });

  describe('handler', () => {
    it('should handle payment processing and return PaymentSuccessful on success', async () => {
      jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');

      const response = await finalPayAmountSubmitted.handler(context);

      expect(finalPayAmountSubmitted.services.coreService.decodePaymentToken).toHaveBeenCalledWith(mockPaymentToken);
      expect(finalPayAmountSubmitted.services.locationService.getLocationConfiguration).toHaveBeenCalledWith(123);
      expect(finalPayAmountSubmitted.services.integrationService.createPaymentHash).toHaveBeenCalled();
      expect(finalPayAmountSubmitted.services.integrationService.getPaymentHashDetails).toHaveBeenCalledWith('hash123');
      expect(finalPayAmountSubmitted.paySelectedUnits).toHaveBeenCalled();
      expect(response.nextState).toBe(PayByPhoneState.PaymentSuccessful);
    });

    it('should return PaymentFailure when payment processing fails', async () => {
      jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits').mockResolvedValue({ data: { success: false }, status: 500 });

      const response = await finalPayAmountSubmitted.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PaymentFailure);
    });

    it('should return PaymentFailure when payment details are incomplete', async () => {
      context.storage.paymentToken = undefined;

      const response = await finalPayAmountSubmitted.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PaymentFailure);
    });

    it('should return PaymentFailure when payment hash details are empty', async () => {
      jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'getPaymentHashDetails').mockResolvedValue({ items: {} } as any);

      const response = await finalPayAmountSubmitted.handler(context);

      expect(response.nextState).toBe(PayByPhoneState.PaymentFailure);
    });
  });

  describe('FinalPayAmountSubmitted - PaymentData amount precision', () => {
    it('should ensure PaymentData.amount has two decimal places', async () => {
      context.storage.selectedUnits = [
        { ledger_id: 1, unit_id: 'unit123', tenant_id: 'tenant123', amount_owed: '100.1234' } as any,
      ];
      jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');

      await finalPayAmountSubmitted.handler(context);

      const paymentDataArray: PaymentData[] = await (finalPayAmountSubmitted.paySelectedUnits as jest.Mock).mock.calls[0][1];

      expect(paymentDataArray[0].amount).toBe(100.12);
    });
  });

  describe('FinalPayAmountSubmitted - Convenience Fee', () => {

    beforeEach(() => {
      context.storage.selectedUnits = [
        { ledger_id: 1, unit_id: 'unit123', tenant_id: 'tenant123', amount_owed: '100.00' } as any,
        { ledger_id: 2, unit_id: 'unit234', tenant_id: 'tenant123', amount_owed: '100.00' } as any
      ];
    });

    it('should add convenience fee to the first unit', async () => {
      jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');

      await finalPayAmountSubmitted.handler(context);

      expect(finalPayAmountSubmitted.paySelectedUnits).toHaveBeenCalled();
      const paymentDataArray: PaymentData[] = await (finalPayAmountSubmitted.paySelectedUnits as jest.Mock).mock.calls[0][1];
      expect(paymentDataArray[0].convenience_fee).toBe(true);
      expect(paymentDataArray[0].convenience_fee_amount).toBe(5);
    });

    it('should not add convenience fee to the second unit', async () => {
      jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');

      await finalPayAmountSubmitted.handler(context);

      expect(finalPayAmountSubmitted.paySelectedUnits).toHaveBeenCalled();
      const paymentDataArray: PaymentData[] = await (finalPayAmountSubmitted.paySelectedUnits as jest.Mock).mock.calls[0][1];
      expect(paymentDataArray[0].convenience_fee).toBe(true);
      expect(paymentDataArray[1].convenience_fee).toBe(false);
    });
  });

  describe('FinalPayAmountSubmitted - Location Bulk Payment', () => {

    describe('When ENABLED', () => {

      beforeEach(() => {
        finalPayAmountSubmitted.services.locationService.getLocationConfiguration = jest.fn().mockResolvedValue({ ...mockLocation, allow_bulk_payment: true });
        context.storage.selectedUnits = [
          { ledger_id: 1, unit_id: 'unit123', tenant_id: 'tenant123', amount_owed: '100.00' } as any,
          { ledger_id: 2, unit_id: 'unit234', tenant_id: 'tenant123', amount_owed: '100.00' } as any
        ];
      });

      it('should make a single bulk payment for all selected units and all payments SUCCEED', async () => {
        jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantBulkPayment');
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantPayment');

        await finalPayAmountSubmitted.handler(context);

        expect(finalPayAmountSubmitted.paySelectedUnits).toHaveBeenCalled();
        expect(finalPayAmountSubmitted.services.integrationService.makeTenantBulkPayment).toHaveBeenCalled();
        expect(finalPayAmountSubmitted.services.integrationService.makeTenantPayment).not.toHaveBeenCalled();
      });

      it('should make a single bulk payment for all selected units and at least one payment FAILS', async () => {
        jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantBulkPayment').mockResolvedValue({ data: { success: false }, status: 500 });
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantPayment');

        await finalPayAmountSubmitted.handler(context);

        expect(finalPayAmountSubmitted.paySelectedUnits).toHaveBeenCalled();
        const resolvedValue = await (finalPayAmountSubmitted.paySelectedUnits as jest.Mock).mock.results[0].value;
        expect(resolvedValue).toEqual({ data: { success: false }, status: 500 });
        expect(finalPayAmountSubmitted.services.integrationService.makeTenantPayment).not.toHaveBeenCalled();
      });

    });

    describe('When DISABLED', () => {

      beforeEach(() => {
        finalPayAmountSubmitted.services.locationService.getLocationConfiguration = jest.fn().mockResolvedValue({ ...mockLocation, allow_bulk_payment: false });
        context.storage.selectedUnits = [
          { ledger_id: 1, unit_id: 'unit123', tenant_id: 'tenant123', amount_owed: '100.00' } as any,
          { ledger_id: 2, unit_id: 'unit234', tenant_id: 'tenant123', amount_owed: '100.00' } as any
        ];
      });

      it('should make a single payment for each selected unit and all payments SUCCEED', async () => {
        jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantBulkPayment');
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantPayment');

        await finalPayAmountSubmitted.handler(context);

        expect(finalPayAmountSubmitted.paySelectedUnits).toHaveBeenCalled();
        expect(finalPayAmountSubmitted.services.integrationService.makeTenantBulkPayment).not.toHaveBeenCalled();
        expect(finalPayAmountSubmitted.services.integrationService.makeTenantPayment).toHaveBeenCalled();
      }, 10 * 60 * 1000);

      it('should make a single payment for each selected unit and at least one payment FAILS', async () => {
        jest.spyOn(finalPayAmountSubmitted, 'paySelectedUnits');
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantBulkPayment');
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantPayment').mockResolvedValue({ data: { success: true }, status: 201 });
        jest.spyOn(finalPayAmountSubmitted.services.integrationService, 'makeTenantPayment').mockResolvedValueOnce({ data: { success: false }, status: 500 });

        await finalPayAmountSubmitted.handler(context);

        expect(finalPayAmountSubmitted.paySelectedUnits).toHaveBeenCalled();
        const resolvedValue = await (finalPayAmountSubmitted.paySelectedUnits as jest.Mock).mock.results[0].value;
        expect(resolvedValue).toEqual({ data: { success: false }, status: 500 });
        expect(finalPayAmountSubmitted.services.integrationService.makeTenantBulkPayment).not.toHaveBeenCalled();
      }, 10 * 60 * 1000);

    });

  });

});
