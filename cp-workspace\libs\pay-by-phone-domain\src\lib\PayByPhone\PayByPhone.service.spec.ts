import { TestingModule } from '@nestjs/testing';
import { PayByPhoneService } from './PayByPhone.service';
import { PayByPhoneContextService } from './PayByPhoneContext.service';
import { PayByPhoneRequestBody, PayByPhoneResponse, PayByPhoneStorage } from './PayByPhone.model';
import { PayByPhoneState } from './Generated/PayByPhoneState.generated';
import { I18nTranslationService } from './I18nTranslation.service';
import { PayByPhoneTest } from './PayByPhoneTest.module';
import { PayByPhoneStorageService } from './PayByPhoneStorage.service';
import { AsyncWorkersService, Locale,
  RedisService } from '@cp-workspace/shared';
import { FinalPayAmountSubmitted } from './States/FinalPayAmountSubmitted';

describe('PayByPhoneService', () => {
  let testModuleRef: TestingModule;
  let service: PayByPhoneService;
  let contextService: PayByPhoneContextService;
  let i18n: I18nTranslationService;
  let storageService: PayByPhoneStorageService;
  let workersService: AsyncWorkersService;


  beforeEach(async () => {
    const module: TestingModule = await PayByPhoneTest.createTestingModule([FinalPayAmountSubmitted]);
    service = module.get<PayByPhoneService>(PayByPhoneService);
    contextService = module.get<PayByPhoneContextService>(PayByPhoneContextService);
    i18n = module.get<I18nTranslationService>(I18nTranslationService);
    storageService = module.get<PayByPhoneStorageService>(PayByPhoneStorageService);
    workersService = module.get<AsyncWorkersService>(AsyncWorkersService);
    testModuleRef = module;
  });

  afterEach(async () => {
    await RedisService.stop();
  });

  describe('handleRequest', () => {
    it('should handle disconnected call and return empty TwiML', async () => {
      const request = {
        CallSid: "CA0a5cf2e34bb14dcaeea9344b16c7416d",
        LocationId: 462,
        CallStatus: 'completed',
      } as PayByPhoneRequestBody;

      const storage = {
        state: "CustomerByPhoneSearch",
        locale: "en-US",
        transferToAgentUrl: "https://example.com/transfer",
        locationId: 462,
        phoneNumber: "6143852455",
        payByPhoneStartTime: 1718387787715,
        origin: "callroute"
      } as PayByPhoneStorage;

      const expectedTwiML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response/>";

      jest.spyOn(storageService, 'getStorage').mockResolvedValue(storage);
      jest.spyOn(storageService, 'deleteStorage').mockResolvedValue(undefined);

      const result = await service.handleRequest(request);

      expect(result).toBe(expectedTwiML);
      expect(storageService.getStorage).toHaveBeenCalledWith(expect.any(String));
      expect(storageService.deleteStorage).toHaveBeenCalledWith(expect.any(String));
    });

    it('should handle disconnected call with no storage and return empty TwiML', async () => {
      const request = {
        CallSid: "CA0a5cf2e34bb14dcaeea9344b16c7416d",
        LocationId: 462,
        CallStatus: 'completed',
      } as PayByPhoneRequestBody;

      const expectedTwiML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response/>";

      jest.spyOn(storageService, 'getStorage').mockResolvedValue(undefined);
      jest.spyOn(storageService, 'deleteStorage').mockResolvedValue(undefined);

      const result = await service.handleRequest(request);

      expect(result).toBe(expectedTwiML);
      expect(storageService.getStorage).toHaveBeenCalledWith(expect.any(String));
      expect(storageService.deleteStorage).not.toHaveBeenCalled();
    });

    it('should handle the request and return a Twilio VoiceResponse', async () => {
      const request = {
        "CallSid": "CA0a5cf2e34bb14dcaeea9344b16c7416d",
        "LocationId": 462,
        "OverrideLocale": "en-US",
        "From": "6143852455",
        "TransferToAgentUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?managerConnect=1&payment_process=1",
        "PaymentSuccessRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=success&payment_process=1",
        "PaymentFailureRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=failure&payment_process=1",
        "Origin": "callroute"
      } as PayByPhoneRequestBody;

      const storage = {
        "state": "CustomerByPhoneSearch",
        "locale": "en-US",
        "transferToAgentUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?managerConnect=1&payment_process=1",
        "locationId": 462,
        "phoneNumber": "6143852455",
        "paymentSuccessRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=success&payment_process=1",
        "paymentFailureRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=failure&payment_process=1",
        "payByPhoneStartTime": 1718387787715,
        "origin": "callroute"
      } as PayByPhoneStorage;

      const expectedTwiML = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Redirect>pay-by-phone</Redirect></Response>";

      jest.spyOn(storageService, 'getStorage').mockResolvedValue(storage);
      jest.spyOn(service, 'handleState').mockResolvedValue({ nextState: PayByPhoneState.InputPhoneGather });

      const result = await service.handleRequest(request);

      expect(result).toBe(expectedTwiML);

    });
  });

  describe('handleState', () => {
    it('should handle the state and return a PayByPhoneStateHandlerResponse', async () => {
      const request = {
        "CallSid": "CA0a5cf2e34bb14dcaeea9344b16c7416d",
        "LocationId": 462,
        "OverrideLocale": "en-US",
        "From": "6143852455",
        "TransferToAgentUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?managerConnect=1&payment_process=1",
        "PaymentSuccessRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=success&payment_process=1",
        "PaymentFailureRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=failure&payment_process=1",
        "Origin": "callroute"
      } as PayByPhoneRequestBody;

      const twilioResponse = new PayByPhoneResponse(i18n);

      const storage = {
        "state": "InputPhoneGather",
        "locale": "en-US",
        "transferToAgentUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?managerConnect=1&payment_process=1",
        "locationId": 462,
        "phoneNumber": "6143852455",
        "paymentSuccessRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=success&payment_process=1",
        "paymentFailureRedirectUrl": "https://call-api.callpdev.com/qa-4446/v1/twilio/process_next_step/13709730706688892?PaymentState=failure&payment_process=1",
        "payByPhoneStartTime": 1718387787715,
        "origin": "callroute",
        "absoluteUrl": "pay-by-phone",
      } as PayByPhoneStorage;

      const stateHandlerResponse = { nextState: PayByPhoneState.InputPhoneValidate };

      jest.spyOn(service, 'isValidTransitionState').mockReturnValue(true);

      const result = await service.handleState(request, twilioResponse, storage);

      expect(service['isValidTransitionState']).toHaveBeenCalledWith(storage.state, stateHandlerResponse.nextState);
      expect(result).toEqual(stateHandlerResponse);
    });

    it('should throw an error for an invalid state transition', async () => {
      const request = {
        CallSid: '123',
        LocationId: 456,
      } as PayByPhoneRequestBody;
      const twilioResponse = new PayByPhoneResponse(i18n);
      const storage = {
        state: PayByPhoneState.LocalePrompt,
        locationId: 456,
        locale: 'en-US',
        transferToAgentUrl: '/transfer-to-agent',
      } as PayByPhoneStorage;
      const stateHandlerResponse = { nextState: PayByPhoneState.PaymentSuccessful };

      jest.spyOn(service.stateHandlers, 'getStateHandler').mockResolvedValue({ handler: jest.fn().mockResolvedValue(stateHandlerResponse), services: contextService });

      await expect(service.handleState(request, twilioResponse, storage)).rejects.toThrowError(
        `Invalid state transition from ${storage.state} to ${stateHandlerResponse.nextState}`
      );
    });

    describe('Long Running State Handlers', () => {
      let finalPayAmountSubmitted: FinalPayAmountSubmitted;

      beforeEach(async () => {
        finalPayAmountSubmitted = testModuleRef.get<FinalPayAmountSubmitted>(FinalPayAmountSubmitted);
        jest.spyOn(finalPayAmountSubmitted, 'handler').mockResolvedValue({ nextState: PayByPhoneState.PaymentSuccessful });
      });

      describe('when invoked from a request', () => {

        describe('and handler IS NOT YET INVOKED asynchronously', () => {

          it('should invoke the handler asynchronously and loopback', async () => {
            const expectedTwimlResponse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Redirect>pay-by-phone</Redirect></Response>";
            const expectedStateHandlerResponse = { loopback: true, doInvokeLongRunningProcess: true };
            jest.spyOn(finalPayAmountSubmitted, 'invokeLongRunningProcess').mockResolvedValue(expectedStateHandlerResponse);
            jest.spyOn(service, 'invokeAsyncStateHandler');
            // jest.spyOn(workersService, 'invokeAsyncWorker');
            jest.spyOn(storageService, 'saveStorage');

            const request = {
              CallSid: '123',
              LocationId: 456,
              Source: 'request',
            } as PayByPhoneRequestBody;
            const twilioResponse = new PayByPhoneResponse(i18n);
            const storage = {
              state: PayByPhoneState.FinalPayAmountSubmitted,
              locationId: 456,
              locale: 'en-US',
              transferToAgentUrl: '/transfer-to-agent',
            } as PayByPhoneStorage;

            const actualStateHandlerResponse = await service.handleState(request, twilioResponse, storage);
            const actualTwimlResponse = await service.redirect(actualStateHandlerResponse, request, storage, twilioResponse);
            console.log('actualRedirectResponse', actualTwimlResponse);
            expect(actualStateHandlerResponse).toEqual(expectedStateHandlerResponse);
            expect(actualTwimlResponse).toBe(expectedTwimlResponse);
            expect(finalPayAmountSubmitted.invokeLongRunningProcess).toHaveBeenCalled();
            expect(service.invokeAsyncStateHandler).toHaveBeenCalledWith(request, storage);
            // expect(workersService.invokeAsyncWorker).toHaveBeenCalledWith({
            //   source: 'async-invocation',
            //   body: request
            // });
            // expect(storageService.saveStorage).toHaveBeenCalledTimes(2);

          }, 10 * 60 * 1000);

        });

        describe('and handler IS INVOKED asynchronously and running in a separate process', () => {

          it('should check the async process status and loopback if in-progress with a say verb for processing payment', async () => {
            const request = {
              CallSid: '123',
              LocationId: 456,
              Source: 'request',
            } as PayByPhoneRequestBody;

            const inProgressStorage = {
              state: "FinalPayAmountSubmitted",
              locationId: 456,
              locale: "en-US",
              transferToAgentUrl: "/transfer-to-agent",
            } as PayByPhoneStorage;

            const mainStorage = {
              state: "FinalPayAmountSubmitted",
              locationId: 456,
              locale: "en-US",
              transferToAgentUrl: "/transfer-to-agent",
              stateInProgress: "FinalPayAmountSubmitted",
            } as PayByPhoneStorage;

            const twilioResponse = new PayByPhoneResponse(i18n);

            storageService.initialize({ data: {
              [`in_progress_${request.CallSid}`]: JSON.stringify(inProgressStorage),
              [request.CallSid]: JSON.stringify(mainStorage),
            } });

            const expectedTwimlResponse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Say language=\"en-US\">Test Translation</Say><Redirect>pay-by-phone</Redirect></Response>";
            const expectedStateHandlerResponse = { loopback: true };

            jest.spyOn(finalPayAmountSubmitted, 'isWatchingForCompletion');
            jest.spyOn(finalPayAmountSubmitted, 'checkForCompletion');
            jest.spyOn(finalPayAmountSubmitted, 'loopbackAfterTimeout').mockImplementation(async (loopbackCount: number) => {
              return finalPayAmountSubmitted.loopbackNow(loopbackCount);
            });
            jest.spyOn(storageService, 'saveStorage');

            const actualStateHandlerResponse = await service.handleState(request, twilioResponse, mainStorage);
            const actualTwimlResponse = await service.redirect(actualStateHandlerResponse, request, mainStorage, twilioResponse);

            expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveBeenCalledWith(mainStorage);
            expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveReturnedWith(true);
            expect(finalPayAmountSubmitted.checkForCompletion).toHaveBeenCalledWith(inProgressStorage, mainStorage);
            expect(actualStateHandlerResponse).toEqual(expectedStateHandlerResponse);
            expect(actualTwimlResponse).toBe(expectedTwimlResponse);
            expect(storageService.saveStorage).toHaveBeenLastCalledWith(request.CallSid, {
              ...mainStorage,
              loopbackCount: 1,
            });

          });

          describe('when loopback count reaches max loopback', () => {

            it('should transition to the failure state and loopback', async () => {
              const request = {
                CallSid: '123',
                LocationId: 456,
                Source: 'request',
              } as PayByPhoneRequestBody;

              const inProgressStorage = {
                state: "FinalPayAmountSubmitted",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
              } as PayByPhoneStorage;

              const mainStorage = {
                state: "FinalPayAmountSubmitted",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
                stateInProgress: "FinalPayAmountSubmitted",
                loopbackCount: 20,
              } as PayByPhoneStorage;

              const finalStorage = {
                state: "PaymentFailure",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
              } as PayByPhoneStorage;

              const twilioResponse = new PayByPhoneResponse(i18n);

              storageService.initialize({ data: {
                [`in_progress_${request.CallSid}`]: JSON.stringify(inProgressStorage),
                [request.CallSid]: JSON.stringify(mainStorage),
              } });

              const expectedTwimlResponse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Redirect>pay-by-phone</Redirect></Response>";
              const expectedStateHandlerResponse = { loopback: true, nextState: PayByPhoneState.PaymentFailure };

              jest.spyOn(finalPayAmountSubmitted, 'isWatchingForCompletion');
              jest.spyOn(finalPayAmountSubmitted, 'checkForCompletion');
              jest.spyOn(finalPayAmountSubmitted, 'loopbackAfterTimeout').mockImplementation(async (loopbackCount: number) => {
                return finalPayAmountSubmitted.loopbackNow(loopbackCount);
              });
              jest.spyOn(storageService, 'saveStorage');

              const actualStateHandlerResponse = await service.handleState(request, twilioResponse, mainStorage);
              const actualTwimlResponse = await service.redirect(actualStateHandlerResponse, request, mainStorage, twilioResponse);

              expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveBeenCalledWith(mainStorage);
              expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveReturnedWith(true);
              expect(finalPayAmountSubmitted.checkForCompletion).toHaveBeenCalledWith(inProgressStorage, mainStorage);
              expect(actualStateHandlerResponse).toEqual(expectedStateHandlerResponse);
              expect(actualTwimlResponse).toBe(expectedTwimlResponse);
              expect(storageService.saveStorage).toHaveBeenLastCalledWith(request.CallSid, finalStorage);
            });

          });

        });

        describe('and handler IS INVOKED asynchronously and completed in separate process', () => {

          describe('and async process SUCCEEDED', () => {

            it('should update storage accordingly and return the async process result', async () => {
              const expectedTwimlResponse = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><Response><Redirect>pay-by-phone</Redirect></Response>";
              const request = {
                CallSid: '123',
                LocationId: 456,
                Source: 'request',
              } as PayByPhoneRequestBody;

              const inProgressStorage = {
                state: "PaymentSuccessful",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
                stateInProgressResult: {
                  twimlResponse: expectedTwimlResponse,
                },
              } as PayByPhoneStorage;

              const mainStorage = {
                state: "FinalPayAmountSubmitted",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
                stateInProgress: "FinalPayAmountSubmitted",
              } as PayByPhoneStorage;

              const finalStorage = {
                state: "PaymentSuccessful",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
              } as PayByPhoneStorage;

              const twilioResponse = new PayByPhoneResponse(i18n);

              storageService.initialize({ data: {
                [`in_progress_${request.CallSid}`]: JSON.stringify(inProgressStorage),
                [request.CallSid]: JSON.stringify(mainStorage),
              } });

              const expectedStateHandlerResponse = { loopback: true, doReturnLongRunningResult: true, longRunningResult: { twimlResponse: expectedTwimlResponse }, newStorage: finalStorage };

              jest.spyOn(finalPayAmountSubmitted, 'isWatchingForCompletion');
              jest.spyOn(finalPayAmountSubmitted, 'checkForCompletion');
              jest.spyOn(finalPayAmountSubmitted, 'loopbackAfterTimeout').mockImplementation(async (loopbackCount: number) => {
                return finalPayAmountSubmitted.loopbackNow(loopbackCount);
              });
              jest.spyOn(storageService, 'saveStorage');

              const actualStateHandlerResponse = await service.handleState(request, twilioResponse, mainStorage);
              const actualTwimlResponse = await service.redirect(actualStateHandlerResponse, request, mainStorage, twilioResponse);


              expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveBeenCalledWith(mainStorage);
              expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveReturnedWith(true);
              expect(finalPayAmountSubmitted.checkForCompletion).toHaveBeenCalledWith(inProgressStorage, mainStorage);


              expect(actualStateHandlerResponse).toEqual(expectedStateHandlerResponse);
              expect(actualTwimlResponse).toBe(expectedTwimlResponse);
              expect(storageService.saveStorage).toHaveBeenLastCalledWith(request.CallSid, finalStorage);
            });

          });

          describe('and async process FAILED with unhandled error', () => {

            it('should update storage accordingly and throw the async process error', async () => {
              const request = {
                CallSid: '123',
                LocationId: 456,
                Source: 'request',
              } as PayByPhoneRequestBody;

              const err = new Error('Unhandled error');
              const errObj = {
                message: err.message,
                name: err.name,
                stack: err.stack,
              }

              const inProgressStorage = {
                state: "PaymentSuccessful",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
                stateInProgressResult: {
                  error: errObj,
                },
              } as PayByPhoneStorage;

              const mainStorage = {
                state: "FinalPayAmountSubmitted",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
                stateInProgress: "FinalPayAmountSubmitted",
              } as PayByPhoneStorage;

              const finalStorage = {
                state: "PaymentSuccessful",
                locationId: 456,
                locale: "en-US",
                transferToAgentUrl: "/transfer-to-agent",
              } as PayByPhoneStorage;

              const twilioResponse = new PayByPhoneResponse(i18n);

              storageService.initialize({ data: {
                [`in_progress_${request.CallSid}`]: JSON.stringify(inProgressStorage),
                [request.CallSid]: JSON.stringify(mainStorage),
              } });

              jest.spyOn(finalPayAmountSubmitted, 'isWatchingForCompletion');
              jest.spyOn(finalPayAmountSubmitted, 'checkForCompletion');
              jest.spyOn(finalPayAmountSubmitted, 'loopbackAfterTimeout').mockImplementation(async (loopbackCount: number) => {
                return finalPayAmountSubmitted.loopbackNow(loopbackCount);
              });
              jest.spyOn(storageService, 'saveStorage');

              const expectedStateHandlerResponse = { loopback: true, doReturnLongRunningResult: true, longRunningResult: { error: errObj }, newStorage: finalStorage };
              const actualStateHandlerResponse = await service.handleState(request, twilioResponse, mainStorage);
              expect(service.redirect(actualStateHandlerResponse, request, mainStorage, twilioResponse)).rejects.toThrow(new Error('Unhandled error'));

              expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveBeenCalledWith(mainStorage);
              expect(finalPayAmountSubmitted.isWatchingForCompletion).toHaveReturnedWith(true);
              expect(finalPayAmountSubmitted.checkForCompletion).toHaveBeenCalledWith(inProgressStorage, mainStorage);


              expect(actualStateHandlerResponse).toEqual(expectedStateHandlerResponse);
              expect(storageService.saveStorage).toHaveBeenLastCalledWith(request.CallSid, finalStorage);
            });

          });

        });

      });

      describe('when invoked from an async-invocation', () => {

        it('should execute the state handler function', async () => {
          const request = {
            CallSid: '123',
            LocationId: 456,
            Source: 'async-invocation',
          } as PayByPhoneRequestBody;
          const twilioResponse = new PayByPhoneResponse(i18n);
          const storage = {
            state: PayByPhoneState.FinalPayAmountSubmitted,
            locationId: 456,
            locale: 'en-US',
            transferToAgentUrl: '/transfer-to-agent',
          } as PayByPhoneStorage;
          const stateHandlerResponse = { nextState: PayByPhoneState.PaymentSuccessful };

          const result = await service.handleState(request, twilioResponse, storage);

          expect(result).toEqual(stateHandlerResponse);
          expect(finalPayAmountSubmitted.handler).toHaveBeenCalledWith({ request, twilioResponse, storage });
        });

      });

    });

  });

  describe('redirect', () => {
    it('should redirect to the specified URL for exit states', async () => {
      const result = { nextState: PayByPhoneState.DisconnectCall, redirectUrl: '/exit-url' };
      const request = {
        CallSid: '123',
        LocationId: 456,
      } as PayByPhoneRequestBody;
      const storage = {
        state: PayByPhoneState.DisconnectCall,
        locationId: 456,
        locale: 'en-US',
        transferToAgentUrl: '/transfer-to-agent',
      } as PayByPhoneStorage;
      const twilioResponse = new PayByPhoneResponse(i18n);

      jest.spyOn(storageService, 'saveStorage').mockResolvedValue(undefined as any);
      jest.spyOn(storageService, 'deleteStorage').mockResolvedValue(undefined as any);
      jest.spyOn(twilioResponse, 'redirect');

      const response = await service.redirect(result, request, storage, twilioResponse);

      expect(storageService.deleteStorage).toHaveBeenCalledWith(request.CallSid);
      expect(storageService.saveStorage).not.toHaveBeenCalled();
      expect(twilioResponse.redirect).toHaveBeenCalledWith(result.redirectUrl);
      expect(response).toBe(twilioResponse.toString());
    });

    it('should update the storage state and redirect for non-exit states', async () => {
      const result = { nextState: PayByPhoneState.LocaleConfirm };
      const request = {
        CallSid: '123',
        LocationId: 456,
      } as PayByPhoneRequestBody;
      const storage = {
        locale: Locale.English,
        locationId: 456,
        state: PayByPhoneState.LocalePrompt,
        transferToAgentUrl: '/transfer-to-agent',
      } as PayByPhoneStorage;
      const twilioResponse = new PayByPhoneResponse(i18n);

      jest.spyOn(storageService, 'saveStorage').mockResolvedValue(undefined as any);
      jest.spyOn(storageService, 'deleteStorage').mockResolvedValue(undefined as any);
      jest.spyOn(twilioResponse, 'redirect');

      const response = await service.redirect(result, request, storage, twilioResponse);

      expect(storageService.deleteStorage).not.toHaveBeenCalled();
      expect(storageService.saveStorage).toHaveBeenCalledWith(request.CallSid, storage);
      expect(twilioResponse.redirect).toHaveBeenCalledWith('pay-by-phone');
      expect(response).toBe(twilioResponse.toString());
    });
  });

  describe('isValidTransitionState', () => {
    it('should return true for a valid state transition', () => {
      const currentState = PayByPhoneState.LocalePrompt;
      const nextState = PayByPhoneState.LocaleConfirm;
      const result = service['isValidTransitionState'](currentState, nextState);
      expect(result).toBe(true);
    });

    it('should return false for an invalid state transition', () => {
      const currentState = PayByPhoneState.LocalePrompt;
      const nextState = PayByPhoneState.PaymentSuccessful;
      const result = service['isValidTransitionState'](currentState, nextState);
      expect(result).toBe(false);
    });
  });

  describe('Entry State', () => {
    it('should determine the entry state', () => {
      const request = {
        CallSid: '123',
        LocationId: 456,
        OverrideEntryState: PayByPhoneState.SayAmountDue,
      } as PayByPhoneRequestBody;

      const result = service['determineEntryState'](request);

      expect(result).toBe(request.OverrideEntryState);
    });

    it('should determine the default entry state if not overridden', () => {
      const request = {
        CallSid: '123',
        LocationId: 456,
      } as PayByPhoneRequestBody;

      const result = service['determineEntryState'](request);

      expect(result).toBe(service['defaultEntryState']);
    });

    it('should determine the entry locale', () => {
      const request = {
        CallSid: '123',
        LocationId: 456,
        OverrideLocale: Locale.French,
      } as PayByPhoneRequestBody;

      const result = service['determineEntryLocale'](request);

      expect(result).toBe(request.OverrideLocale);
    });

    it('should determine the default entry locale if not overridden', () => {
      const request = {
        CallSid: '123',
        LocationId: 456,
      } as PayByPhoneRequestBody;

      const result = service['determineEntryLocale'](request);

      expect(result).toBe(service['defaultEntryLocale']);
    });

    it('should determine the entry storage state', async () => {
      const request = {
        CallSid: '123',
        OverrideEntryState: PayByPhoneState.SayAmountDue,
        OverrideLocale: 'en-US',
        LocationId: 456,
        TransferToAgentUrl: '/transfer-to-agent',
        From: '************',
        PaymentSuccessRedirectUrl: '/success',
        PaymentFailureRedirectUrl: '/failure',
        Origin: 'collections',
        TenantId: 'tenant123',
      };

      const result = await service['determineEntryStorageState'](request as any);

      expect(result).toEqual({
        state: request.OverrideEntryState,
        locale: request.OverrideLocale,
        transferToAgentUrl: request.TransferToAgentUrl,
        locationId: request.LocationId,
        phoneNumber: request.From,
        paymentSuccessRedirectUrl: request.PaymentSuccessRedirectUrl,
        paymentFailureRedirectUrl: request.PaymentFailureRedirectUrl,
        origin: request.Origin,
        payByPhoneStartTime: expect.any(Number),
        tenantId: request.TenantId,
        convenienceFee: NaN,
        absoluteUrl: expect.any(String),
        toNumber: undefined,
        totalAmountDue: 0,
        totalBalance: 0
      });
    });
  });
});
