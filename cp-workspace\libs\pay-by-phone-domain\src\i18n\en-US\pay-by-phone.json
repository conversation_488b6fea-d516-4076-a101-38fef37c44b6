{"collections-prompt-pbp-allowed": "To make a payment, please press 1. To speak with a manager, press 2. To be removed from our list, press 9.", "collections-prompt-pbp-not-allowed": "To speak with a manager, press 2. To be removed from our list, press 9.", "opt-out-success": "Your phone number has been removed from our list.", "locale-select": "For English, press {0.optionNumber}.", "total-balance": "Your total balance due is {0.totalBalance}. To make a payment please press 1. To speak with a manager, press 0", "account-found": "We have located an account belonging to {0.tenantName}, for unit number {1.unitName}. Your total balance due is {2.unitBalance}. Your next scheduled payment date is {3.paymentDate}. To make a payment, please press 1. To speak with a manager, press 0.", "ask-prepay": "Please press the dial pad number representing the number of pre-payments you would like to make. For example, to pay one month in advance, press 1, or, to pay 2 months in advance, press 2. Please note: the automated system does not accept partial payments.", "ask-prepay-zero-balance": "Cannot pay a 0 balance.", "prepay-selection": "You have selected to pay {0.numPayments} payment for a total of {1.prepayAmountDue}. To process your payment, press 1. To change the amount of the payment, press 2.", "ask-saved-cc": "If you would like to use one of your previously saved credit cards, please enter the last 4 digits now, or, to enter a new card, press 0 followed by star.", "ask-for-number": "Please enter the main phone number associated with your account or, to speak with a manager, press 0 followed by the pound key.", "account-not-found": "We are unable to locate your account.", "must-enter-ten-digits": "You must enter a 10 digit phone number.", "multiple-accounts": "We located multiple accounts matching your phone number.", "pay-all": "To pay all units, press 1.", "account-select": "If you are calling about {1.tenantName} in Unit {2.unitName}, press {0.keyPress}.", "start-over": "To start over, press 9.", "speak-manager": "To speak with a manager, press 0 followed by the pound key.", "not-allowed": "Pay by phone is not allowed on this account.", "max-retry": "Sorry, Maximum retry attempts reached.", "card-not-found": "We were unable to find a matching card.", "amount-due": "The total amount that will be charged to your card is {0.paymentAmount}.", "enter-cc": "Please enter the card number, followed by star.", "enter-expiry": "Please enter the expiration date on the card using two digits for the month and the last two digits of the year, followed by star.", "enter-ccv": "Please enter the security code, followed by star.", "enter-zip": "Please enter the zip code associated with the card, followed by star.", "confirm-payment": "To confirm the information entered is correct and process your payment, press 1.", "invalid-input": "Invalid entry.", "payment-under-process": "Please wait. Your Payment is being processed.", "payment-success": "Your payment has been processed successfully.", "payment-fail": "We were unable to process your payment. Please wait while we connect you to a manager.", "cc-confirm": "The number you entered is {0.enteredNumber}. To confirm the number is correct, Press 1, or, to re-enter the number, press 2.", "expiration-confirm": "The expiration date you entered is {0.enteredDate}. To confirm this is correct, press 1, or, to re-enter an expiration date, press 2.", "security-code-confirm": "The security code you entered is {0.enteredCvc}. To confirm this is correct, press 1, or, to re-enter a security code, press 2.", "zip-confirm": "The zip code you entered is {0.enteredZip}. To confirm this is correct, press 1, or, to re-enter a zip code, press 2.", "wait-for-account-fetch": "Please wait while we gather your account details.", "could-not-find-card": "We were unable to find a matching card."}