import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class ExistingPayMethodVerify extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Verify if the last 4 digits of the card matches any saved cards
     * 2. If it matches, transition to PayMethodSecurityCodePrompt
     * 3. If it does not match, compose TwiML to say we were unable to find a matching card and transition to PayMethodCreditCardPrompt
     */

    const { twilioResponse, storage } = context;

    if (storage.savedCards) {
      const matchedCard = storage.savedCards.find(
        (card) => card.card_number === storage.lastFourDigits
      );

      if (matchedCard) {
        storage.useSavedCardId = matchedCard.id;
        return { nextState: PayByPhoneState.PayMethodSecurityCodePrompt };
      }
    }

    twilioResponse.sayInLocale({
      messageId: 'pay-by-phone.could-not-find-card',
      locale: context.storage.locale
    });
    return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
  }
}
