import { Injectable } from "@nestjs/common";
import { PayByPhoneStateContext, PayByPhoneLongRunningState, LoopbackPayByPhoneStateHandlerResponse } from "../PayByPhone.model";
import { PayByPhoneState } from "../Generated/PayByPhoneState.generated";
import { Customer } from "@cp-workspace/shared";

@Injectable()
export class GetSavedCards extends PayByPhoneLongRunningState {
  constructor() {
    super(PayByPhoneState.GetSavedCards, PayByPhoneState.PayMethodCreditCardPrompt);
  }
  override async handler(context: PayByPhoneStateContext): Promise<LoopbackPayByPhoneStateHandlerResponse> {
    const { storage } = context;

    const locationConfiguration = await this.services.locationService.getLocationConfiguration(storage.locationId!);

    let filterByLedgers: string[] = [];
    if(storage.selectedUnits) {
      filterByLedgers = storage.selectedUnits.map(unit => unit.ledger_id.toString());
    }

    storage.savedCards = [];
    if ((storage.matchedTenants && storage.matchedTenants?.length === 1) && storage.selectedTenant) {
      storage.savedCards = locationConfiguration.allow_prev_cc === 1 ? await Customer.getSavedCards([storage.selectedTenant], this.services.integrationService, filterByLedgers) : [];
      storage.savedCards.forEach(card => {
        card.card_number = card.card_number.slice(-4);
      });
    }

    const hasSavedCards = storage.savedCards.length > 0;
    if (hasSavedCards) {
      return { nextState: PayByPhoneState.PayMethodPrompt };
    }

    return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
  }
}
