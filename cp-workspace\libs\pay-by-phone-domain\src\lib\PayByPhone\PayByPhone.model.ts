import VoiceResponse from 'twilio/lib/twiml/VoiceResponse';
import { PayByPhoneState } from './Generated/PayByPhoneState.generated';
import { TranslateOptions } from 'nestjs-i18n';
import {
  Locale,
  AccountService,
  LocationService,
  CoreService,
  IntegrationService,
  BugsnagService,
  DataDogService,
  Tenant,
  SavedCard,
  Ledger,
  PaymentToken,
  TenantId,
  RedisService,
} from '@cp-workspace/shared';
import { I18nTranslationService } from './I18nTranslation.service';
import { PayByPhoneContextService } from './PayByPhoneContext.service';
import { I18nPath } from "./Generated/i18n.generated";

export type PayByPhoneStateHandlerMap = Record<PayByPhoneState, typeof PayByPhoneStateBase>;

export class PayByPhoneStateBase {
  public services!: PayByPhoneContextService;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  handler(context: PayByPhoneStateContext): Promise<PayByPhoneStateHandlerResponseBase> {
    throw new Error('Method not implemented.');
  }
}

export class PayByPhoneLongRunningState extends PayByPhoneStateBase{
  protected static readonly DEFAULT_WAIT_TIMEOUT: number = 3000;
  protected static readonly DEFAULT_MAX_LOOPBACK_COUNT: number = 20;
  longRunningState: PayByPhoneState;
  waitTimeout: number;
  maxLoopbackCount: number;
  maxLoopbackState: PayByPhoneState;

  /**
   *
   * @param longRunningState The pay-by-phone state that is being executed asynchronously
   * @param waitTimeout The amount of time to wait before looping back to check for completion
   * @param maxLoopbackCount The maximum number of times to loop back before giving up
   * @param maxLoopbackState The state to transition to when the maxLoopbackCount is reached
   */
  constructor(longRunningState: PayByPhoneState, maxLoopbackState: PayByPhoneState, waitTimeout = PayByPhoneLongRunningState.DEFAULT_WAIT_TIMEOUT, maxLoopbackCount = PayByPhoneLongRunningState.DEFAULT_MAX_LOOPBACK_COUNT) {
    super();
    this.longRunningState = longRunningState;
    this.waitTimeout = waitTimeout;
    this.maxLoopbackState = maxLoopbackState;
    this.maxLoopbackCount = maxLoopbackCount;
  }

  isWatchingForCompletion(storage: PayByPhoneStorage): boolean {
    return (storage.stateInProgress && storage.stateInProgress === this.longRunningState) ? true : false;
  }

  async checkForCompletion(workerStorage: PayByPhoneStorage, mainStorage: PayByPhoneStorage): Promise<LoopbackPayByPhoneStateHandlerResponse> {
    if (workerStorage.stateInProgressResult && (workerStorage.stateInProgressResult.twimlResponse || workerStorage.stateInProgressResult.error)) {
      const longRunningResult = workerStorage.stateInProgressResult;
      const newStorage = { ...workerStorage };
      delete newStorage.stateInProgressResult;
      return { loopback: true, doReturnLongRunningResult: true, longRunningResult, newStorage };
    }
    return this.loopbackAfterTimeout(mainStorage.loopbackCount || 0);
  }

  async loopbackAfterTimeout(loopbackCount: number): Promise<LoopbackPayByPhoneStateHandlerResponse> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(this.loopbackNow(loopbackCount));
      }, this.waitTimeout);
    });
  }

  loopbackNow(loopbackCount: number): LoopbackPayByPhoneStateHandlerResponse {
    if (loopbackCount >= this.maxLoopbackCount) {
      return { loopback: true, nextState: this.maxLoopbackState };
    } else {
      return { loopback: true };
    }
  }

  invokeLongRunningProcess(): LoopbackPayByPhoneStateHandlerResponse | PromiseLike<LoopbackPayByPhoneStateHandlerResponse> {
    return { loopback: true, doInvokeLongRunningProcess: true };
  }
}

export interface PayByPhoneLongRunningResult {
  twimlResponse?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error?: any;
}

export type PayByPhoneStateContext = {
  request: PayByPhoneRequestBody;
  twilioResponse: PayByPhoneResponse;
  storage: PayByPhoneStorage;
};

export type PayByPhoneServiceContext = {
  i18n: I18nTranslationService;
  redisService: RedisService;
  accountService: AccountService;
  locationService: LocationService;
  coreService: CoreService;
  integrationService: IntegrationService;
  dataDogService: DataDogService;
  bugsnagService: BugsnagService;
};

export type PayByPhoneSayAttributes = {
  messageId: I18nPath,
  locale: Locale,
  i18nOptions?: TranslateOptions,
  sayOptions?: VoiceResponse.SayAttributes
};

export class PayByPhoneResponse extends VoiceResponse {
  i18n: I18nTranslationService;
  constructor(
    i18n: I18nTranslationService,
  ) {
    super();
    this.i18n = i18n;
  }
  sayInLocale(attributes: PayByPhoneSayAttributes): VoiceResponse.Say {
    const text = this.i18n.translate(attributes.messageId, {
      lang: attributes.locale,
      ...attributes.i18nOptions,
    }) as string;
    const options = { language: attributes.locale, ...attributes.sayOptions };
    return this.say(options, text);
  }

  gatherWithLocaleSay(
    gatherOptions: VoiceResponse.GatherAttributes,
    attributeArray: PayByPhoneSayAttributes[],
  ): VoiceResponse.Gather {
    const gather = this.gather(gatherOptions);

    for (const attribute of attributeArray) {
      const text = this.i18n.translate(attribute.messageId, { lang: attribute.locale, ...attribute.i18nOptions }) as string;
      const options = { language: attribute.locale, ...attribute.sayOptions };
      gather.say(options, text);
    }

    return gather;
  }

  sayAccountFoundDetails(selectedUnit: Ledger, selectedTenant: Tenant, storage: PayByPhoneStorage): void {
    const nextPaymentDate = new Date(selectedUnit.paid_thru_date);
    nextPaymentDate.setDate(nextPaymentDate.getDate() + 1);

    this.gatherWithLocaleSay({
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.account-found',
      locale: storage.locale,
      i18nOptions: { args: [
        {tenantName: `${selectedTenant.first_name} ${selectedTenant.last_name}`},
        {unitName: selectedUnit.unit_name},
        {unitBalance: (Number(selectedUnit.amount_owed) + Number(storage.convenienceFee)).toFixed(2) },
        {paymentDate: nextPaymentDate.toLocaleDateString(storage.locale) }
      ]}
    }]);
  }

  sayTotalBalance(storage: PayByPhoneStorage): void {
    this.gatherWithLocaleSay({
      numDigits: 1,
      method: 'POST',
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.total-balance',
      locale: storage.locale,
      i18nOptions: { args: [{ totalBalance: (storage.totalBalance + storage.convenienceFee).toFixed(2) }] }
    }]);
  }
}

export type PayByPhoneStateHandlerResponseBase = {
  nextState?: PayByPhoneState;
};

export interface PayByPhoneStateHandlerResponse
  extends PayByPhoneStateHandlerResponseBase {
  nextState: PayByPhoneState;
}

export interface ExitPayByPhoneStateHandlerResponse
  extends PayByPhoneStateHandlerResponseBase {
  nextState?: PayByPhoneState;
  redirectUrl?: string;
}

export interface LoopbackPayByPhoneStateHandlerResponse
  extends PayByPhoneStateHandlerResponseBase {
  loopback?: boolean;
  doInvokeLongRunningProcess?: boolean;
  doReturnLongRunningResult?: boolean;
  longRunningResult?: PayByPhoneLongRunningResult;
  newStorage?: PayByPhoneStorage;
}

export interface PayByPhoneRequestBody {
  CallSid: string;
  LocationId: number;
  TransferToAgentUrl: string;
  From?: string;
  To?: string;
  Digits?: string;
  OverrideEntryState?: PayByPhoneState;
  OverrideLocale?: Locale | string;
  PaymentSuccessRedirectUrl?: string;
  PaymentFailureRedirectUrl?: string;
  TenantId?: string;
  CallStatus?: 'completed' | string;
  Origin?: 'collections' | 'callroute';
  Source?: 'request' | 'async-invocation'; // Default is 'request'
  StorageKeyPrefix?: string; // This is used for async-invocation
}

export type PayByPhoneStorage = {
  state: PayByPhoneState;
  locationId: number;
  locale: Locale;
  transferToAgentUrl: string;
  payByPhoneStartTime?: number;
  origin?: 'unknown' | 'collections' | 'callroute';
  phoneNumber?: string;
  toNumber?: string;
  retryCount?: number;
  matchedTenants?: Tenant[];
  savedCards?: SavedCard[];
  useSavedCardId?: string;
  selectedUnits?: Ledger[];
  selectedUnit?: Ledger; // This is assigned when there is a single selected unit.
  selectedTenant?: Tenant;
  isDelinquentPaymentFlow?: boolean; // This is used to determine if the payment flow is for delinquent units
  multipleUnitsFound?: boolean;
  payingAllUnits?: boolean;
  payByPhoneAllowed?: boolean;
  paymentSuccessRedirectUrl?: string;
  paymentFailureRedirectUrl?: string;
  paymentProcessorResponse?: unknown;
  prepayMonths?: number;
  lastFourDigits?: string;
  paymentToken?: PaymentToken;
  tenantId?: TenantId;
  totalBalance: number;
  totalAmountDue: number;
  convenienceFee: number;
  absoluteUrl?: string;
  /**
   * This is used to store the state that is actively executing a
   * long running process, which spans multiple requests. Having
   * this flag helps us understand which state we are in the middle
   * of processing.
   */
  stateInProgress?: PayByPhoneState;
  /**
   * When an asynchronous worker is invoked, it will store its result
   * here.  This is used to determine when the async worker has completed
   * its work.
   */
  stateInProgressResult?: PayByPhoneLongRunningResult;
  /**
   * PayByPhoneLongRunningState handling entails looping back to check for completion.
   * This is the count of how many times we have looped back which is used to compare
   * against the maxLoopbackCount.
   */
  loopbackCount?: number;
};

export function storageKey(request: PayByPhoneRequestBody) {
  return `${request.StorageKeyPrefix || ''}${request.CallSid}`;
}
