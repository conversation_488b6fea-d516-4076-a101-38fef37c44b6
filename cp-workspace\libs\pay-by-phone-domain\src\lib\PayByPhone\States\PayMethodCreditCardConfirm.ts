import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodCreditCardConfirm extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. Read the customer's confirmation selection
     * 2. If they have confirmed the number
     * 2a. Save card number in storage
     * 2b. Transisiton to PayMethodExpirationPrompt
     * 3. If they reject, they want to re-enter their number, so we'll transisiton back to PayMethodCreditCardPrompt
     */
    const { request } = context;

    const confirmationSelection = request.Digits;

    if (confirmationSelection === '1') {
      return { nextState: PayByPhoneState.PayMethodExpirationPrompt };
    } else {
      return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
    }
  }
}
