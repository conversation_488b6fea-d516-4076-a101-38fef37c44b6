const Twilio = require('twilio');

/**
 * NOTE: This function is duplicated in two places inside of this project.  The reason for this
 * is because the resulting lambda functions (twilio, conference-event) deployed from this project 
 * use different root folders and there is no way to share code between them.
 * 
 * This function is called by the `conference-event` lambda function.
 * 
 * TODO: As we continue to reorganize this project, we will structure common code so that it can
 * be used by both lambda functions
 */
async function updateReservationStatus(twilioClient, workspace, taskSid, reservationSid, agentCallSid) {

  return await workspace
    .tasks(taskSid)
    .reservations(reservationSid)
    .update({ reservationStatus: 'accepted' })
    .catch(async (error) => {
      console.log(error, new Error().stack);
      // If error in accepting reservation, end the agent call
      await updateCallStatus(
        twilioClient,
        agentCallSid,
        'completed'
      );
    });
}

async function updateCallStatus(twilioClient, callSid, status) {
  try {
    
    await twilioClient
    .calls(callSid)
    .update({ status });

  } catch (e) {
    console.log(e, new Error().stack);
  }
}

/**
 * NOTE: This function is duplicated in two places inside of this project.  The reason for this
 * is because the resulting lambda functions (twilio, conference-event) deployed from this project 
 * use different root folders and there is no way to share code between them.
 * 
 * This function is called by the `conference-event` lambda function.
 * 
 * TODO: As we continue to reorganize this project, we will structure common code so that it can
 * be used by both lambda functions
 */
async function dialCustomerInConference(twilioClient, customerCallSid, conferenceFriendlyName) {

  const conferenceParams = {
    participantLabel: "customer",
    startConferenceOnEnter: true,
    endConferenceOnExit: true,
  };

  const confTwiml = new Twilio.twiml.VoiceResponse();
  const dial = confTwiml.dial();
  dial.conference(conferenceParams, conferenceFriendlyName);

  try {
    console.debug("Dial customer in conference", customerCallSid, conferenceFriendlyName);
    await twilioClient
    .calls(customerCallSid)
    .update({
      twiml: confTwiml.toString(),
    });

  } catch(e) {
    console.log(e, new Error().stack);
  }

}



module.exports = {
  updateReservationStatus,
  dialCustomerInConference,
};
