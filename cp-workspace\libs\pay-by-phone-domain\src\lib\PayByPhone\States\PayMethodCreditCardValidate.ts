import { Injectable } from '@nestjs/common';
import {
  PayByPhoneStateContext,
  PayByPhoneStateHandlerResponse,
  PayByPhoneStateBase,
} from '../PayByPhone.model';
import { PaymentToken } from '@cp-workspace/shared';
import { PayByPhoneState } from '../Generated/PayByPhoneState.generated';

@Injectable()
export class PayMethodCreditCardValidate extends PayByPhoneStateBase {
  override async handler(
    context: PayByPhoneStateContext
  ): Promise<PayByPhoneStateHandlerResponse> {
    /**
     * 1. If CC is invalid
     * 1a. Compose TwiML to ask for credit card input
     * 1b. Compose the TwiML that will gather credit card input
     * 1c. Transition to PayMethodCreditCardValidate
     * 2. If CC is valid
     * 2a. Compose TwiML to ask for confirmation
     * 2b. Compose TwiML to gather the input
     * 2c. Transition to PayMethodCreditCardConfirm
     */

    const { request, twilioResponse, storage } = context;
    const coreClient = this.services.coreService;
    const enteredNumber = request.Digits;
    const allowedDigitRange = [15,19];

    if (!enteredNumber || enteredNumber.length < allowedDigitRange[0] || enteredNumber.length > allowedDigitRange[1]) {
      twilioResponse.sayInLocale({
        messageId: 'pay-by-phone.invalid-input',
        locale: context.storage.locale
      });
      return { nextState: PayByPhoneState.PayMethodCreditCardPrompt };
    }

    const paymentToken: PaymentToken = {
      cardNumber: enteredNumber,
    };

    const encodedToken = await coreClient.encodePaymentToken(paymentToken);

    storage.paymentToken = encodedToken;

    twilioResponse.gatherWithLocaleSay({
      method: 'POST',
      numDigits: 1,
      timeout: 10,
    }, [{
      messageId: 'pay-by-phone.cc-confirm',
      locale: storage.locale,
      i18nOptions: { args: [{ enteredNumber: enteredNumber.split('').join(' ') }] }
    }]);


    return { nextState: PayByPhoneState.PayMethodCreditCardConfirm };
  }
}
